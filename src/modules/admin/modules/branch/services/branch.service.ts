import { BadRequestException, Injectable, NotFoundException, UnprocessableEntityException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Not, Repository } from 'typeorm';
import { Branch } from '../../../../../common/modules/database/entities/branch.entity';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { License } from '../../../../../common/modules/database/entities/license.entity';
import { Timezone } from '../../../../../common/modules/database/entities/timezone.entity';
import { CreateSubBranchDto } from '../dto/create-sub-branch.dto';
import { CodeGeneratorService } from '../../../../../common/services/code-generator.service';
import { GetBranchesDto } from '../dto/get-branches.dto';
import { UserBranch } from '../../../../../common/modules/database/entities/user-branch.entity';
import { Role } from '../../../../../common/modules/database/entities/role.entity';
@Injectable()
export class BranchService {
  constructor(
    private dataSource: DataSource,
    @InjectRepository(Branch)
    private readonly branchRepository: Repository<Branch>,
    @InjectRepository(License)
    private readonly licenseRepository: Repository<License>,
    @InjectRepository(Timezone)
    private readonly timezoneRepository: Repository<Timezone>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private codeGeneratorService: CodeGeneratorService,
  ) { }

  async getBranches(queries: GetBranchesDto, user: User) {
    let branchId = user.parent_branch_id;

    // If branchCode is provided, find the branch ID from the branch code
    if (queries.branch_code) {
      const targetBranch = await this.branchRepository.findOne({
        where: { branch_code: queries.branch_code },
        select: ['parent_id']
      });

      if (targetBranch) {
        branchId = targetBranch.parent_id;
      }
    }

    // Create query builder to join with user_branches
    const queryBuilder = this.branchRepository
      .createQueryBuilder('branch')
      .leftJoinAndSelect('branch.timezone', 'timezone')
      .leftJoinAndSelect('branch.license', 'license')
      .leftJoinAndSelect('branch.parent', 'parent')
      .where('branch.parent_id = :parentId', {
        parentId: branchId,
      });

    if (!user.system_access) {
      // Hide main branch if not super admin
      if (!user?.role?.super_admin) {
        queryBuilder.andWhere('branch.id != branch.parent_id');
      }

      queryBuilder
        .innerJoin('user_branches', 'ub', 'ub.branch_id = branch.id')
        .where('ub.user_id = :userId', { userId: user.id });

      // Add active condition if ignore_active_status is false
      if (!queries.ignore_active_status) {
        queryBuilder.andWhere('branch.active = :active', { active: true });
      }
    }

    // Add search condition
    if (queries.search) {
      queryBuilder.andWhere('branch.branch_name ILIKE :search', {
        search: `%${queries.search}%`,
      });
    }

    // Add ordering - optional with default to created_at DESC
    queryBuilder.orderBy(
      `branch.${queries.order_by || 'created_at'}`,
      queries.order_direction || 'DESC',
    );

    // Add pagination - only if both page and limit are provided
    if (queries.page && queries.limit) {
      queryBuilder.skip((queries.page - 1) * queries.limit);
      queryBuilder.take(queries.limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      data: items,
      meta: {
        total,
        page: queries.page || 1,
        limit: queries.limit || total,
        total_pages: queries.limit ? Math.ceil(total / queries.limit) : 1,
      },
    };
  }

  async getBranchById(id: number, user: User) {
    const foundBranch = await this.branchRepository.findOne({
      where: {
        id,
        parent: { id: user.parent_branch_id },
      },
      relations: ['license', 'timezone', 'parent'],
    });

    if (!foundBranch) {
      throw new NotFoundException(`Branch with ID ${id} not found`);
    }

    return foundBranch;
  }

  async createSubBranch(
    createSubBranchDto: CreateSubBranchDto,
    user: User,
  ): Promise<Branch> {
    console.log('branch_code:', createSubBranchDto.branch_code);
    
    let branchId = user.parent_branch_id;

    if (!branchId && createSubBranchDto.branch_code) {
      const targetBranch = await this.branchRepository.findOne({
        where: { branch_code: createSubBranchDto.branch_code },
        select: ['parent_id']
      });
      if (targetBranch) {
        branchId = targetBranch.parent_id;
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const countSubBranch = await this.branchRepository.count({
      where: {
        parent_id: branchId,
        id: Not(branchId),
      },
    });

    const parentBranch = await this.branchRepository.findOne({
      where: {
        id: branchId,
      },
      relations: ['license'],
    });

    if (!parentBranch) {
      throw new NotFoundException('Parent branch not found');
    }

    const license = await this.licenseRepository.findOne({
      where: {
        id: parentBranch.license.id,
      },
    });

    if (!license) {
      throw new NotFoundException('License not found');
    }

    const maxSubbranch = license.max_subbranch;
    if (countSubBranch >= maxSubbranch) {
      throw new NotFoundException('You have reached the limit of your license; please upgrade your license.');
    }

    try {

      // Validate timezone exists
      const timezone = await this.timezoneRepository.findOne({
        where: { id: createSubBranchDto.timezone },
      });

      if (!timezone) {
        throw new NotFoundException('Timezone not found');
      }

      // Use the code generator service
      const branchCode = await this.codeGeneratorService.generateBranchCode();

      // Create branch
      const newBranch = this.branchRepository.create({
        branch_code: branchCode,
        branch_name: createSubBranchDto.name,
        branch_description: createSubBranchDto.description,
        timezone: timezone,
        license: license,
        active: createSubBranchDto.active,
        parent_id: branchId,
        created_by: user.id,
        updated_by: user.id,
      });

      const savedBranch = await queryRunner.manager.save(Branch, newBranch);

      // insert to user branches
      await queryRunner.manager.insert(UserBranch, {
        user_id: user.id,
        branch_id: savedBranch.id,
        created_by: user.id,
        updated_by: user.id,
        created_at: new Date(),
        updated_at: new Date(),
        active: true,
      });

      // find super admin roles
      const superAdminRole = await this.roleRepository.findOne({
        where: {
          super_admin: true,
          parent_branch_id: branchId,
        },
      });

      if (superAdminRole) {
        const findUsersWithSuperAdminRole = await this.userRepository.find({
          where: {
            role_id: superAdminRole.id,
          },
        });

        if (findUsersWithSuperAdminRole) {
          for (const user of findUsersWithSuperAdminRole) {
            await queryRunner.manager.insert(UserBranch, {
              user_id: user.id,
              branch_id: savedBranch.id,
              created_by: user.id,
              updated_by: user.id,
              created_at: new Date(),
              updated_at: new Date(),
              active: true,
            });
          }
        }
      }

      await queryRunner.commitTransaction();

      return this.getBranchById(savedBranch.id, user);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  public async updateSubBranch(
    id: number,
    updateSubBranchDto: CreateSubBranchDto,
    user: User,
  ): Promise<Branch> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Find and validate existing branch
      const existingBranch = await this.branchRepository.findOne({
        where: {
          id,
          parent: { id: user.parent_branch_id },
        },
        relations: ['license', 'timezone', 'parent'],
      });

      if (!existingBranch) {
        throw new NotFoundException(`Branch with ID ${id} not found`);
      }

      // Validate timezone exists
      const timezone = await this.timezoneRepository.findOne({
        where: { id: updateSubBranchDto.timezone },
      });

      if (!timezone) {
        throw new NotFoundException('Timezone not found');
      }

      // Update branch using QueryBuilder instead of Active Record
      await queryRunner.manager
        .createQueryBuilder()
        .update(Branch)
        .set({
          branch_name: updateSubBranchDto.name,
          timezone_id: timezone.id,
          active: updateSubBranchDto.active,
          branch_description: updateSubBranchDto.description,
          updated_by: user.id,
          updated_at: new Date(),
        })
        .where('id = :id', { id })
        .execute();

      await queryRunner.commitTransaction();

      return this.getBranchById(id, user);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  public async deleteSubBranch(id: number, user: User){
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    const existingBranch = await this.branchRepository.findOne({
      where: {
        id,
        parent: { id: user.parent_branch_id },
      },
      relations: ['license', 'timezone', 'parent'],
    });
    if (!existingBranch) {
      throw new NotFoundException(`Branch with ID ${id} not found`);
    }

    try {
      const result = await this.dataSource.query(`
          SELECT
          (SELECT COUNT(*) > 0 FROM activities WHERE branch_id = $1 AND deleted_at IS NULL) AS is_activity_empty,
          (SELECT COUNT(*) > 0 FROM checkpoints WHERE branch_id = $1 AND deleted_at IS NULL) AS is_checkpoint_empty,
          (SELECT COUNT(*) > 0 FROM geofences WHERE branch_id = $1 AND deleted_at IS NULL) AS is_geofence_empty,
          (SELECT COUNT(*) > 0 FROM labels WHERE branch_id = $1 AND deleted_at IS NULL) AS is_label_empty,
          (SELECT COUNT(*) > 0 FROM schedulers WHERE branch_id = $1 AND deleted_at IS NULL) AS is_scheduler_empty,
          (SELECT COUNT(*) > 0 FROM tasks WHERE branch_id = $1 AND deleted_at IS NULL) AS is_task_empty,
          (SELECT COUNT(*) > 0 FROM zones WHERE branch_id = $1 AND deleted_at IS NULL) AS is_zone_empty;
        `, [id]);
      const { is_activity_empty, is_checkpoint_empty, is_geofence_empty, is_label_empty, is_scheduler_empty, is_task_empty, is_zone_empty } = result[0];

      if (is_activity_empty ||
          is_checkpoint_empty ||
          is_geofence_empty ||
          is_label_empty ||
          is_scheduler_empty ||
          is_task_empty ||
          is_zone_empty) {
        throw new BadRequestException('Cannot delete branch with existing activities, checkpoints, geofences, labels, schedulers, tasks,  or zones.')
      }

      await this.branchRepository.createQueryBuilder()
        .update(Branch)
        .set({
          deleted_by: user.id,
        })
        .where('id = :id', {id})
        .execute();
      await queryRunner.manager.softDelete(Branch, {id});
      await queryRunner.commitTransaction();
      return {message: 'Branch deleted'};
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
