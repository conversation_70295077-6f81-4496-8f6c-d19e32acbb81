import { Injectable } from '@nestjs/common';
import { LogActivity } from '../../../database/entities/log-activity.entity';
import axios from 'axios';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import { Branch } from '../../../database/entities/branch.entity';
import { Role } from '../../../database/entities/role.entity';
import { User } from '../../../database/entities/user.entity';
import { Label } from '../../../database/entities/label.entity';
import { Device } from '../../../database/entities/device.entity';
import { Activity } from '../../../database/entities/activity.entity';
import { Timezone } from '../../../database/entities/timezone.entity';

dayjs.extend(timezone);

// Types for internal data structures
interface ActivityLogDisplayData {
  label: string;
  value: string;
}

interface PdfBufferSetup {
  doc: PDFKit.PDFDocument;
  buffers: Buffer[];
}

export interface ActivityLogFiltersGenerateDocument {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  role: Role | null;
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
  activity: Activity | null;
}

@Injectable()
export class ActivityLogGenerateDocumentService {
  // Constants for consistent styling
  private readonly PRIMARY_COLOR = '#1a237e';
  private readonly SECONDARY_COLOR = '#546e7a';
  private readonly TEXT_COLOR = '#263238';
  private readonly LIGHT_GRAY = '#fafafa';
  private readonly BORDER_COLOR = '#e0e0e0';
  private readonly FOOTER_COLOR = '#9e9e9e';
  private readonly COMPANY_NAME = 'UNIGUARD';
  private readonly LOGO_URL = 'https://trial-serverless.web.app/logo.png';
  private readonly FOOTER_TEXT = 'UniGuard Security System';

  constructor() {}

  /**
   * Generates a PDF document for a single activity log entry
   * Creates a professional report with detailed information and photo (if available)
   *
   * @param activityLog - Single activity log entry to generate report for
   * @param timezone
   * @returns Promise resolving to an object containing the PDF buffer and filename
   * @throws {Error} If there's an issue generating the PDF
   *
   * @remarks
   * The generated PDF includes:
   * - Company logo and report metadata
   * - Detailed activity log information in table format
   * - Activity photo (if available)
   * - Professional layout and styling
   */
  async generatePDFById(
    activityLog: LogActivity,
    timezone: Timezone,
  ): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    const { doc, buffers } = this.setupPdfDocument();

    // Add Page
    doc.addPage();

    // Add company header
    await this.addCompanyHeader(doc, timezone);

    // Add title and header
    this.drawSectionHeader(doc, 'Activity Log Details', 120);

    // Add UUID right after header
    this.addUuidSection(doc, activityLog.uuid, 155);

    // Add entry details with improved layout
    let currentY = 190;

    // Transform activity log data and render table
    const { leftColumnData, rightColumnData } = this.transformActivityLogData(activityLog);
    currentY = this.renderDataTable(doc, leftColumnData, rightColumnData, currentY);

    // Add comment section
    currentY = this.addCommentSection(doc, activityLog.comment, currentY);

    // Add photo if available
    currentY = await this.addPhotoSection(doc, activityLog.photo_url, currentY);

    // Add footer
    this.addFooter(doc);

    // Finalize and return PDF
    return this.finalizePdfDocument(doc, buffers, 'activity-log');
  }

  /**
   * Generates a spreadsheet document for a single activity log entry
   * Creates a professional report with detailed information in Excel format
   *
   * @param activityLog - Single activity log entry to generate report for
   * @param timezone
   * @returns Promise resolving to an object containing the spreadsheet buffer and filename
   * @throws {Error} If there's an issue generating the spreadsheet
   *
   * @remarks
   * The generated spreadsheet includes:
   * - Report title and metadata
   * - Detailed activity log information in table format
   * - Photo URL as clickable hyperlink (if available)
   * - Professional formatting and styling
   */
  async generateSpreadsheetById(
    activityLog: LogActivity,
    timezone: Timezone,
  ): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Activity Log');

    // Add title and metadata
    let currentRow = this.addExcelTitle(worksheet, 'UNIGUARD ACTIVITY LOG REPORT', 'G', 1);
    currentRow = this.addExcelGenerationTime(worksheet, timezone, 'G', currentRow);
    currentRow = this.addExcelUuid(worksheet, activityLog.uuid, 'G', currentRow);

    // Add header row
    currentRow = this.addExcelDataHeader(worksheet, ['Field', 'Value'], currentRow);

    // Add data rows
    const dataRows = this.buildActivityLogDataRows(activityLog);
    currentRow = this.addExcelDataRows(worksheet, dataRows, currentRow);

    // Add photo URL if available
    if (activityLog.photo_url) {
      currentRow = this.addExcelPhotoUrl(worksheet, activityLog.photo_url, currentRow);
    }

    // Set column widths and add footer
    this.setExcelColumnWidths(worksheet, [20, 50]);
    this.addExcelFooter(worksheet, 'B', currentRow + 1);

    // Generate and return buffer
    return this.finalizeExcelDocument(workbook, `activity-log-${activityLog.id}`);
  }

  /**
   * Generates a PDF document from multiple activity log entries
   * Creates a professional report with cover page and detailed activity entries
   *
   * @param data - Array of activity log entries
   * @param filters - Object containing all applied filters
   * @param timezone
   * @returns Promise resolving to an object containing the PDF buffer and filename
   * @throws {Error} If there's an issue generating the PDF
   *
   * @remarks
   * The generated PDF includes:
   * - Cover page with company logo and report metadata
   * - Detailed filter information
   * - Individual activity log entries with photos (if available)
   * - Professional layout and styling
   */
  async generatePDF(
    data: LogActivity[],
    filters: ActivityLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    const { doc, buffers } = this.setupPdfDocument();

    // Add cover page with background and company branding
    await this.addCoverPage(doc, 'ACTIVITY LOG REPORT');

    // Add filters and metadata section
    this.addFiltersSection(doc, filters, timezone, data.length, 300);

    // Add footer to cover page
    this.addFooter(doc);

    // Process each activity log entry
    for (let index = 0; index < data.length; index++) {
      await this.addActivityLogPage(doc, data[index], index, data.length);
    }

    // Finalize and return PDF
    return this.finalizePdfDocument(doc, buffers, 'activity-log');
  }

  /**
   * Generates a spreadsheet document from multiple activity log entries
   * Creates a professional report with detailed information in Excel format
   *
   * @param data - Array of activity log entries
   * @param filters - Object containing all applied filters
   * @param timezone
   * @returns Promise resolving to an object containing the spreadsheet buffer and filename
   * @throws {Error} If there's an issue generating the spreadsheet
   *
   * @remarks
   * The generated spreadsheet includes:
   * - Report title and metadata
   * - Detailed filter information
   * - Activity log entries in table format
   * - Photo URLs as clickable hyperlinks (if available)
   * - Professional formatting and styling
   */
  async generateSpreadsheet(
    data: LogActivity[],
    filters: ActivityLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Activity Logs');
    let currentRow = 1;

    // Add title and styling (merged across all columns)
    worksheet.mergeCells('A1:K1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD ACTIVITY LOGS REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // Add generation time and total entries
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')} | Total Entries: ${data.length}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };

    // Add filter information
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: '1A237E' } };

    // Add filter details
    if (filters.startDate && filters.endDate) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Date Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startDate} to ${filters.endDate}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.startTime && filters.endTime) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Time Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startTime} to ${filters.endTime}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.branch) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Branch:';
      worksheet.getCell(`B${currentRow}`).value = filters.branch.branch_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.role) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Role:';
      worksheet.getCell(`B${currentRow}`).value = filters.role.role_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.user) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User:';
      worksheet.getCell(`B${currentRow}`).value = filters.user.name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.user_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.user_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.device) {
      worksheet.getCell(`A${currentRow}`).value = 'Device:';
      worksheet.getCell(`B${currentRow}`).value = filters.device.device_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
      currentRow++;
    } else if (filters.device_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.device_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.activity) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Activity:';
      worksheet.getCell(`B${currentRow}`).value =
        filters.activity.activity_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      currentRow += 2;
      let startRow = currentRow;
      let endRow = currentRow;
      const cellDateRange = worksheet.getCell(`A${currentRow}`);
      cellDateRange.value = 'Date Range:';
      cellDateRange.font = { bold: true };
      cellDateRange.alignment = { horizontal: 'left', vertical: 'middle' };

      // Calculate the number of days between the start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        if (i > 0) {
          currentRow++;
        }
        endRow = currentRow;

        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      if (daysDiff > maxDaysToShow) {
        currentRow++;
        worksheet.getCell(`B${currentRow}`).value = '...';

        // Show last day
        currentRow++;
        endRow = currentRow;
        const dateStr = endDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      // Merge the date range cells
      worksheet.mergeCells(`A${startRow}:A${endRow}`);
    }

    // Add table headers
    const headers = [
      'UUID',
      'Original Time',
      'Branch',
      'Role',
      'User',
      'Activity',
      'Device',
      'Comment',
      'Photo',
      'Latitude',
      'Longitude',
    ];

    // Add header row with styling
    currentRow += 2;
    worksheet.addRow(2);
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '1A237E' },
    };
    headerRow.alignment = { horizontal: 'center' };

    // Add data rows
    currentRow++;
    data.forEach(activityLog => {
      // Create dayjs object with timezone for reuse
      const originalSubmittedTime = dayjs(
        activityLog.original_submitted_time,
      ).tz(activityLog.timezone_name);

      worksheet.getCell(currentRow, 1).value = activityLog.uuid;
      worksheet.getCell(currentRow, 2).value =
        originalSubmittedTime.format('YYYY-MM-DD HH:mm');
      worksheet.getCell(currentRow, 3).value =
        activityLog.branch?.branch_name || '-';
      worksheet.getCell(currentRow, 4).value =
        activityLog.role?.role_name || '-';
      worksheet.getCell(currentRow, 5).value = activityLog.user?.name || '-';
      worksheet.getCell(currentRow, 6).value =
        activityLog.activity?.activity_name || '-';
      worksheet.getCell(currentRow, 7).value =
        activityLog.device?.device_name || '-';
      worksheet.getCell(currentRow, 8).value = activityLog.comment || '-';

      // Add photo URL as hyperlink if available
      if (activityLog.photo_url) {
        worksheet.getCell(currentRow, 9).value = {
          text: 'View Photo',
          hyperlink: activityLog.photo_url,
          tooltip: 'Click to open photo',
        };
      } else {
        worksheet.getCell(currentRow, 9).value = 'No photo';
      }

      worksheet.getCell(currentRow, 10).value = activityLog.latitude || '-';
      worksheet.getCell(currentRow, 11).value = activityLog.longitude || '-';

      // Apply alternating row colors (excluding photo column to preserve hyperlink styling)
      if (currentRow % 2 === 0) {
        for (let i = 1; i <= headers.length; i++) {
          if (i !== 9 || !activityLog.photo_url) { // Skip photo column if it has hyperlink
            worksheet.getCell(currentRow, i).fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'F5F5F5' },
            };
          }
        }
      }

      // Apply hyperlink styling after background colors to ensure it's not overridden
      if (activityLog.photo_url) {
        worksheet.getCell(currentRow, 9).font = {
          color: { argb: '0000FF' },
          underline: true,
        };
      }

      currentRow++;
    });

    // Set column widths
    worksheet.getColumn(1).width = 36; // UUID
    worksheet.getColumn(2).width = 20; // Original Time
    worksheet.getColumn(3).width = 20; // Branch
    worksheet.getColumn(4).width = 20; // Role
    worksheet.getColumn(5).width = 20; // User
    worksheet.getColumn(6).width = 20; // Activity
    worksheet.getColumn(7).width = 20; // Device
    worksheet.getColumn(8).width = 40; // Comment
    worksheet.getColumn(9).width = 15; // Photo
    worksheet.getColumn(10).width = 15; // Latitude
    worksheet.getColumn(11).width = 15; // Longitude

    // Add footer
    currentRow += 2;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:K${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `activity-logs-${dayjs().format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Sets up PDF document with buffer handling
   */
  private setupPdfDocument(): PdfBufferSetup {
    const doc = this.createPdfDoc();
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));
    return { doc, buffers };
  }

  /**
   * Draws a section header with underline
   */
  private drawSectionHeader(doc: PDFKit.PDFDocument, text: string, y: number): void {
    doc
      .fontSize(16)
      .font('Helvetica-Bold')
      .fillColor(this.PRIMARY_COLOR)
      .text(text, 50, y);

    doc
      .moveTo(50, y + 25)
      .lineTo(545, y + 25)
      .lineWidth(1)
      .strokeColor(this.PRIMARY_COLOR)
      .stroke();
  }

  /**
   * Adds a field row with label and value
   */
  private addFieldRow(
    doc: PDFKit.PDFDocument,
    label: string,
    value: string,
    x: number,
    y: number,
    rowHeight: number = 25,
  ): number {
    const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
    const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

    doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(label, x, labelY);

    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.TEXT_COLOR)
      .text(value || '-', x + 90, valueY);

    return y + rowHeight;
  }

  /**
   * Adds a dynamic field row with label and value that adjusts height and width based on content
   */
  private addDynamicFieldRow(
    doc: PDFKit.PDFDocument,
    label: string,
    value: string,
    x: number,
    y: number,
    maxWidth: number = 495,
  ): number {
    // Set font untuk label dan hitung lebar
    doc.font('Helvetica-Bold').fontSize(10);
    const labelWidth = doc.widthOfString(label);
    const labelPadding = 10;
    const dynamicLabelWidth = Math.max(labelWidth + labelPadding, 90);

    // Hitung lebar yang tersedia untuk value
    const availableValueWidth = maxWidth - dynamicLabelWidth - 20; // 20 untuk margin

    // Set font untuk value dan hitung tinggi yang dibutuhkan
    doc.font('Helvetica').fontSize(10);
    const valueText = value || '-';
    const valueHeight = doc.heightOfString(valueText, {
      width: availableValueWidth,
      lineBreak: true
    });

    // Hitung tinggi row berdasarkan konten terbesar
    const minRowHeight = 25;
    const dynamicRowHeight = Math.max(minRowHeight, valueHeight + 10); // 10 untuk padding

    // Hitung posisi vertikal untuk center alignment
    const labelY = y + (dynamicRowHeight - doc.currentLineHeight()) / 2;
    const valueY = y + 5; // Padding atas untuk value

    // Render label
    doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(label, x, labelY);

    // Render value dengan word wrap
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.TEXT_COLOR)
      .text(valueText, x + dynamicLabelWidth, valueY, {
        width: availableValueWidth,
        ellipsis: false,
        lineBreak: true
      });

    return y + dynamicRowHeight;
  }

  /**
   * Loads company logo from URL with fallback
   */
  private async loadLogo(): Promise<Buffer | null> {
    try {
      const response = await axios.get(this.LOGO_URL, {
        responseType: 'arraybuffer',
      });
      return Buffer.from(response.data, 'binary');
    } catch (error) {
      console.error('Error loading logo:', error);
      return null;
    }
  }

  /**
   * Adds company header with logo and generation time
   */
  private async addCompanyHeader(doc: PDFKit.PDFDocument, timezone: Timezone): Promise<void> {
    const logoBuffer = await this.loadLogo();

    if (logoBuffer) {
      doc.image(logoBuffer, 50, 50, { fit: [50, 50] });
    } else {
      // Fallback if logo loading fails
      doc.save().translate(75, 75).rect(-25, -25, 50, 50).fill(this.PRIMARY_COLOR);
    }

    // Add company name next to logo
    doc
      .font('Helvetica-Bold')
      .fontSize(20)
      .fillColor(this.PRIMARY_COLOR)
      .text(this.COMPANY_NAME, 110, 65);

    // Add generation time in header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(
        `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`,
        350,
        65,
      );
  }

  /**
   * Adds UUID section with horizontal line
   */
  private addUuidSection(doc: PDFKit.PDFDocument, uuid: string, y: number): void {
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(`UUID: ${uuid}`, 50, y);

    // Add horizontal line after UUID
    doc
      .moveTo(50, y + 15)
      .lineTo(545, y + 15)
      .lineWidth(0.5)
      .strokeColor(this.BORDER_COLOR)
      .stroke();
  }

  /**
   * Transforms activity log data into display format
   */
  private transformActivityLogData(activityLog: LogActivity): {
    leftColumnData: ActivityLogDisplayData[];
    rightColumnData: ActivityLogDisplayData[];
  } {
    const originalSubmittedTime = dayjs(activityLog.original_submitted_time).tz(
      activityLog.timezone_name,
    );

    const leftColumnData = [
      { label: 'Branch', value: activityLog.branch_name },
      { label: 'Role', value: activityLog.role_name },
      { label: 'User', value: activityLog.user_name },
      { label: 'Activity', value: activityLog.activity_name },
      { label: 'Device', value: activityLog.device_name },
    ];

    const rightColumnData = [
      { label: 'Timezone', value: activityLog.timezone_name },
      { label: 'Latitude', value: activityLog.latitude?.toString() },
      { label: 'Longitude', value: activityLog.longitude?.toString() },
      {
        label: 'Original Time',
        value: originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      },
    ];

    return { leftColumnData, rightColumnData };
  }

  /**
   * Renders data table with alternating background colors
   */
  private renderDataTable(
    doc: PDFKit.PDFDocument,
    leftColumnData: ActivityLogDisplayData[],
    rightColumnData: ActivityLogDisplayData[],
    startY: number,
  ): number {
    let currentY = startY;
    const rowHeight = 30;

    leftColumnData.forEach((item, i) => {
      // Draw alternating background
      if (i % 2 === 0) {
        doc.fillColor(this.LIGHT_GRAY).rect(50, currentY, 495, rowHeight).fill();
      }

      // Draw left column data
      currentY = this.addFieldRow(
        doc,
        item.label + ':',
        item.value,
        50,
        currentY,
        rowHeight,
      );

      // Draw right column data if available
      if (i < rightColumnData.length) {
        this.addFieldRow(
          doc,
          rightColumnData[i].label + ':',
          rightColumnData[i].value,
          300,
          currentY - rowHeight,
          rowHeight,
        );
      }
    });

    return currentY;
  }

  /**
   * Adds comment section to PDF with dynamic sizing
   */
  private addCommentSection(doc: PDFKit.PDFDocument, comment: string, startY: number): number {
    let currentY = startY + 20;
    this.drawSectionHeader(doc, 'Additional Information', currentY);

    currentY += 40;

    // Use dynamic field row for comment
    currentY = this.addDynamicFieldRow(
      doc,
      'Comment:',
      comment || '-',
      50,
      currentY,
      495
    );

    return currentY + 10; // Add small padding after section
  }

  /**
   * Adds photo section to PDF with dynamic sizing and hyperlink
   */
  private async addPhotoSection(doc: PDFKit.PDFDocument, photoUrl: string, startY: number): Promise<number> {
    if (!photoUrl) return startY;

    let currentY = startY + 20;
    this.drawSectionHeader(doc, 'Activity Photo', currentY);
    currentY += 40;

    try {
      const response = await axios.get(photoUrl, {
        responseType: 'arraybuffer',
      });
      const imageBuffer = Buffer.from(response.data, 'binary');

      // Add the actual image with hyperlink to photo URL
      doc.image(imageBuffer, 50, currentY, {
        fit: [200, 150],
        link: photoUrl, // Make the image clickable
      });

      // Add a small text below the image indicating it's clickable
      doc
        .font('Helvetica')
        .fontSize(8)
        .fillColor('#666666')
        .text('(Click image to view full size)', 50, currentY + 155);

      return currentY + 175; // 150 for image + 25 for text and padding
    } catch (error) {
      // If image loading fails, show clickable URL text
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#0066CC')
        .text('Photo: Click here to view', 50, currentY, {
          link: photoUrl,
          underline: true,
        });

      currentY = this.addDynamicFieldRow(
        doc,
        'Status:',
        'Image could not be loaded - click link above to view',
        50,
        currentY + 20,
        495
      );

      return currentY + 10;
    }
  }

  /**
   * Adds footer to PDF
   */
  private addFooter(doc: PDFKit.PDFDocument): void {
    const footerWidth = doc.widthOfString(this.FOOTER_TEXT);
    doc
      .fontSize(10)
      .fillColor(this.FOOTER_COLOR)
      .text(
        this.FOOTER_TEXT,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );
  }

  /**
   * Finalizes PDF document and returns buffer with filename
   */
  private finalizePdfDocument(
    doc: PDFKit.PDFDocument,
    buffers: Buffer[],
    prefix: string,
  ): Promise<{ buffer: Buffer; filename: string }> {
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `${prefix}-${dayjs().format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  /**
   * Formats timezone-aware date
   */
  private formatTimezoneDate(date: Date | string, timezoneName: string): string {
    return dayjs(date).tz(timezoneName).format('YYYY-MM-DD HH:mm');
  }

  // ===== EXCEL HELPER METHODS =====

  /**
   * Adds title to Excel worksheet
   */
  private addExcelTitle(worksheet: ExcelJS.Worksheet, title: string, lastColumn: string, startRow: number): number {
    worksheet.mergeCells(`A${startRow}:${lastColumn}${startRow}`);
    const titleCell = worksheet.getCell(`A${startRow}`);
    titleCell.value = title;
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(startRow).height = 30;
    return startRow + 1;
  }

  /**
   * Adds generation time to Excel worksheet
   */
  private addExcelGenerationTime(worksheet: ExcelJS.Worksheet, timezone: Timezone, lastColumn: string, currentRow: number): number {
    worksheet.mergeCells(`A${currentRow}:${lastColumn}${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${this.formatTimezoneDate(new Date(), timezone.timezone_name)}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };
    return currentRow + 1;
  }

  /**
   * Adds UUID to Excel worksheet
   */
  private addExcelUuid(worksheet: ExcelJS.Worksheet, uuid: string, lastColumn: string, currentRow: number): number {
    worksheet.mergeCells(`A${currentRow}:${lastColumn}${currentRow}`);
    const uuidCell = worksheet.getCell(`A${currentRow}`);
    uuidCell.value = `UUID: ${uuid}`;
    uuidCell.font = { size: 10, color: { argb: '546E7A' } };
    return currentRow + 1;
  }

  /**
   * Adds data header row to Excel worksheet
   */
  private addExcelDataHeader(worksheet: ExcelJS.Worksheet, headers: string[], currentRow: number): number {
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    return currentRow + 1;
  }

  /**
   * Builds data rows for activity log
   */
  private buildActivityLogDataRows(activityLog: LogActivity): string[][] {
    const originalSubmittedTime = this.formatTimezoneDate(
      activityLog.original_submitted_time,
      activityLog.timezone_name,
    );

    return [
      ['Branch', activityLog.branch_name || '-'],
      ['Role', activityLog.role_name || '-'],
      ['User', activityLog.user_name || '-'],
      ['Activity', activityLog.activity_name || '-'],
      ['Device', activityLog.device_name || '-'],
      ['Timezone', activityLog.timezone_name || '-'],
      ['Latitude', activityLog.latitude?.toString() || '-'],
      ['Longitude', activityLog.longitude?.toString() || '-'],
      ['Original Time', originalSubmittedTime],
      ['Comment', activityLog.comment || '-'],
    ];
  }

  /**
   * Adds data rows to Excel worksheet with alternating colors
   */
  private addExcelDataRows(worksheet: ExcelJS.Worksheet, dataRows: string[][], currentRow: number): number {
    dataRows.forEach((row, index) => {
      const excelRow = worksheet.addRow(row);
      // Add alternating row colors for better readability
      if (index % 2 === 1) {
        excelRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FAFAFA' },
        };
      }
    });

    return currentRow + dataRows.length;
  }

  /**
   * Adds photo URL as hyperlink to Excel worksheet
   */
  private addExcelPhotoUrl(worksheet: ExcelJS.Worksheet, photoUrl: string, currentRow: number): number {
    const photoRow = worksheet.addRow([
      'Photo',
      {
        text: 'View Photo',
        hyperlink: photoUrl,
        tooltip: 'Click to open photo',
      }
    ]);

    // Apply styling directly to the row cells for consistency
    // Style the first column with light green background to highlight photo availability
    photoRow.getCell(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E8F5E9' },
    };
    
    // Style the hyperlink cell with blue color and underline
    photoRow.getCell(2).font = {
      color: { argb: '0000FF' }, // Blue color for hyperlink
      underline: true,
    };
    // Keep hyperlink cell background white to ensure blue text is visible
    photoRow.getCell(2).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFFF' },
    };

    return currentRow + 1;
  }

  /**
   * Sets column widths for Excel worksheet
   */
  private setExcelColumnWidths(worksheet: ExcelJS.Worksheet, widths: number[]): void {
    widths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });
  }

  /**
   * Adds footer to Excel worksheet
   */
  private addExcelFooter(worksheet: ExcelJS.Worksheet, lastColumn: string, currentRow: number): void {
    worksheet.mergeCells(`A${currentRow}:${lastColumn}${currentRow}`);
    const footerCell = worksheet.getCell(`A${currentRow}`);
    footerCell.value = this.FOOTER_TEXT;
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };
  }

  /**
   * Finalizes Excel document and returns buffer with filename
   */
  private async finalizeExcelDocument(workbook: ExcelJS.Workbook, prefix: string): Promise<{ buffer: Buffer; filename: string }> {
    const buffer = await workbook.xlsx.writeBuffer();
    return {
      buffer: buffer as Buffer,
      filename: `${prefix}-${dayjs().format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }

  // ===== PDF COVER PAGE AND FILTERS HELPER METHODS =====

  /**
   * Adds cover page with background and company branding
   */
  private async addCoverPage(doc: PDFKit.PDFDocument, title: string): Promise<void> {
    doc.addPage();

    // Add background
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    // Add company logo and name
    const logoBuffer = await this.loadLogo();

    if (logoBuffer) {
      doc.image(logoBuffer, (doc.page.width - 80) / 2, 100, { fit: [80, 80] });
    } else {
      // Fallback if logo loading fails
      doc
        .save()
        .translate(doc.page.width / 2, 140)
        .rect(-40, -40, 80, 80)
        .fill(this.PRIMARY_COLOR);
    }

    // Add company name
    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor(this.PRIMARY_COLOR)
      .text(
        this.COMPANY_NAME,
        (doc.page.width - doc.widthOfString(this.COMPANY_NAME)) / 2,
        190,
      );

    // Add title
    doc.font('Helvetica-Bold').fontSize(18).fillColor(this.PRIMARY_COLOR);
    const titleWidth = doc.widthOfString(title);
    doc.text(title, (doc.page.width - titleWidth) / 2, 240);
  }

  /**
   * Adds filters section to cover page
   */
  private addFiltersSection(
    doc: PDFKit.PDFDocument,
    filters: ActivityLogFiltersGenerateDocument,
    timezone: Timezone,
    totalEntries: number,
    startY: number,
  ): number {
    // Add active filters section header
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor(this.PRIMARY_COLOR)
      .text('Active Filters', 50, startY);

    doc
      .moveTo(50, startY + 25)
      .lineTo(545, startY + 25)
      .lineWidth(1)
      .strokeColor(this.PRIMARY_COLOR)
      .stroke();

    let currentY = startY + 30;

    // Build filter data
    const leftFilters = this.buildLeftFilters(filters);
    const rightFilters = this.buildRightFilters(filters);

    // Render filters in two columns
    const rowHeight = 25;
    const maxRows = Math.max(leftFilters.length, rightFilters.length);

    for (let i = 0; i < maxRows; i++) {
      if (i < leftFilters.length) {
        this.addFieldRow(doc, leftFilters[i].label, leftFilters[i].value, 50, currentY, rowHeight);
      }
      if (i < rightFilters.length) {
        this.addFieldRow(doc, rightFilters[i].label, rightFilters[i].value, 300, currentY, rowHeight);
      }
      currentY += rowHeight;
    }

    // Add generation info
    doc.font('Helvetica').fontSize(12).fillColor(this.SECONDARY_COLOR);
    currentY = this.addFieldRow(
      doc,
      'Report Generated:',
      this.formatTimezoneDate(new Date(), timezone.timezone_name),
      50,
      currentY,
    );
    currentY = this.addFieldRow(doc, 'Total Entries:', totalEntries.toString(), 50, currentY);

    // Add date range explanation if applicable
    if (filters.startDate && filters.endDate) {
      currentY = this.addDateRangeExplanation(doc, filters, currentY);
    }

    return currentY;
  }

  /**
   * Builds left column filter data
   */
  private buildLeftFilters(filters: ActivityLogFiltersGenerateDocument): { label: string; value: string }[] {
    return [
      {
        label: 'Start Date:',
        value: filters.startDate ? dayjs(filters.startDate).format('MMMM D, YYYY') : 'All Dates',
      },
      {
        label: 'End Date:',
        value: filters.endDate ? dayjs(filters.endDate).format('MMMM D, YYYY') : 'All Dates',
      },
      {
        label: 'Branch:',
        value: filters.branch ? filters.branch.branch_name : 'All Branches',
      },
      {
        label: 'User:',
        value: filters.user ? filters.user.name : 'All Users',
      },
      {
        label: 'Device:',
        value: filters.device ? filters.device.device_name : 'All Devices',
      },
      {
        label: 'Activity:',
        value: filters.activity ? filters.activity.activity_name : 'All Activities',
      },
    ];
  }

  /**
   * Builds right column filter data
   */
  private buildRightFilters(filters: ActivityLogFiltersGenerateDocument): { label: string; value: string }[] {
    return [
      {
        label: 'Start Time:',
        value: filters.startTime || 'All Times',
      },
      {
        label: 'End Time:',
        value: filters.endTime || 'All Times',
      },
      {
        label: 'Role:',
        value: filters.role ? filters.role.role_name : 'All Roles',
      },
      {
        label: 'User Labels:',
        value: filters.user_labels?.length > 0
          ? filters.user_labels.map(label => label.label_name).join(', ')
          : 'All Labels',
      },
      {
        label: 'Device Labels:',
        value: filters.device_labels?.length > 0
          ? filters.device_labels.map(label => label.label_name).join(', ')
          : 'All Labels',
      },
    ];
  }

  /**
   * Adds date range explanation section
   */
  private addDateRangeExplanation(
    doc: PDFKit.PDFDocument,
    filters: ActivityLogFiltersGenerateDocument,
    startY: number,
  ): number {
    let currentY = startY + 20;

    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor(this.PRIMARY_COLOR)
      .text('Data From:', 50, currentY);

    currentY += 25;

    const startDate = dayjs(filters.startDate);
    const endDate = dayjs(filters.endDate);
    const daysDiff = endDate.diff(startDate, 'day') + 1;
    const startTime = filters.startTime || '00:00';
    const endTime = filters.endTime || '23:59';

    const maxDaysToShow = 3;
    const daysToShow = Math.min(daysDiff, maxDaysToShow);

    for (let i = 0; i < daysToShow; i++) {
      const currentDate = startDate.add(i, 'day');
      const dateStr = currentDate.format('YYYY-MM-DD');
      const timeRangeStr = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor(this.TEXT_COLOR)
        .text(timeRangeStr, 50, currentY + i * 20);
    }

    if (daysDiff > maxDaysToShow) {
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor(this.TEXT_COLOR)
        .text('...', 50, currentY + maxDaysToShow * 20);

      const lastDate = endDate.format('YYYY-MM-DD');
      const lastTimeRangeStr = `${lastDate} ${startTime} until ${lastDate} ${endTime}`;

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor(this.TEXT_COLOR)
        .text(lastTimeRangeStr, 50, currentY + (maxDaysToShow + 1) * 20);

      return currentY + (maxDaysToShow + 2) * 20;
    }

    return currentY + daysToShow * 20;
  }

  /**
   * Adds individual activity log page
   */
  private async addActivityLogPage(
    doc: PDFKit.PDFDocument,
    log: LogActivity,
    index: number,
    totalEntries: number,
  ): Promise<void> {
    doc.addPage();

    // Add page header with UUID
    this.drawSectionHeader(doc, `Activity Log Entry #${index + 1}`, 50);
    this.addUuidSection(doc, log.uuid, 85);

    // Add entry details
    let currentY = 120;
    const { leftColumnData, rightColumnData } = this.transformActivityLogDataFromEntity(log);
    currentY = this.renderDataTable(doc, leftColumnData, rightColumnData, currentY);

    // Add comment section
    currentY = this.addCommentSection(doc, log.comment, currentY);

    // Add photo if available
    await this.addPhotoSection(doc, log.photo_url, currentY);

    // Add page number
    this.addPageNumber(doc, index + 2, totalEntries + 1);
  }

  /**
   * Transforms activity log entity data for display (handles entity relationships)
   */
  private transformActivityLogDataFromEntity(log: LogActivity): {
    leftColumnData: ActivityLogDisplayData[];
    rightColumnData: ActivityLogDisplayData[];
  } {
    const originalSubmittedTime = this.formatTimezoneDate(
      log.original_submitted_time,
      log.timezone_name,
    );

    const leftColumnData = [
      { label: 'Branch', value: log.branch?.branch_name },
      { label: 'Role', value: log.role?.role_name },
      { label: 'User', value: log.user?.name },
      { label: 'Activity', value: log.activity?.activity_name },
      { label: 'Device', value: log.device?.device_name },
    ];

    const rightColumnData = [
      { label: 'Timezone', value: log.timezone?.timezone_name },
      { label: 'Latitude', value: log.latitude?.toString() },
      { label: 'Longitude', value: log.longitude?.toString() },
      { label: 'Original Time', value: originalSubmittedTime },
    ];

    return { leftColumnData, rightColumnData };
  }

  /**
   * Adds page number to PDF
   */
  private addPageNumber(doc: PDFKit.PDFDocument, currentPage: number, totalPages: number): void {
    const pageText = `Page ${currentPage} of ${totalPages}`;
    const pageWidth = doc.widthOfString(pageText);
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.FOOTER_COLOR)
      .text(pageText, (doc.page.width - pageWidth) / 2, doc.page.height - 30);
  }

  private createPdfDoc = () => {
    return new PDFDocument({
      size: 'A4',
      autoFirstPage: false,
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      info: {
        Title: 'Activity Log Report',
        Author: 'UniGuard System',
        Creator: 'UniGuard',
      },
    });
  };
}
