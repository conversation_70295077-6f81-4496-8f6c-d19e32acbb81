import { Injectable } from '@nestjs/common';
import { LogActivity } from '../../../database/entities/log-activity.entity';
import axios from 'axios';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import { Branch } from '../../../database/entities/branch.entity';
import { Role } from '../../../database/entities/role.entity';
import { User } from '../../../database/entities/user.entity';
import { Label } from '../../../database/entities/label.entity';
import { Device } from '../../../database/entities/device.entity';
import { Activity } from '../../../database/entities/activity.entity';
import { Timezone } from '../../../database/entities/timezone.entity';

dayjs.extend(timezone);

// ===== TYPES AND INTERFACES =====

interface DocumentDisplayData {
  label: string;
  value: string;
}

interface DocumentGenerationResult {
  buffer: Buffer;
  filename: string;
}

interface PdfDocumentSetup {
  doc: PDFKit.PDFDocument;
  buffers: Buffer[];
}

interface DocumentStyling {
  colors: {
    primary: string;
    secondary: string;
    text: string;
    lightGray: string;
    border: string;
    footer: string;
  };
  fonts: {
    sizes: {
      title: number;
      header: number;
      normal: number;
      small: number;
    };
  };
}

interface CompanyBranding {
  name: string;
  logoUrl: string;
  footerText: string;
}

export interface ActivityLogFiltersGenerateDocument {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  role: Role | null;
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
  activity: Activity | null;
}

// ===== CONFIGURATION OBJECTS =====

const DOCUMENT_STYLING: DocumentStyling = {
  colors: {
    primary: '#1a237e',
    secondary: '#546e7a',
    text: '#263238',
    lightGray: '#fafafa',
    border: '#e0e0e0',
    footer: '#9e9e9e',
  },
  fonts: {
    sizes: {
      title: 20,
      header: 16,
      normal: 10,
      small: 8,
    },
  },
};

const COMPANY_BRANDING: CompanyBranding = {
  name: 'UNIGUARD',
  logoUrl: 'https://trial-serverless.web.app/logo.png',
  footerText: 'UniGuard Security System',
};

const PDF_CONFIG = {
  pageSize: 'A4' as const,
  margins: { top: 10, bottom: 10, left: 10, right: 10 },
  rowHeight: 30,
  columnWidth: 495,
  leftColumnX: 50,
  rightColumnX: 300,
};

const EXCEL_CONFIG = {
  columnWidths: {
    uuid: 36,
    time: 20,
    standard: 20,
    comment: 40,
    photo: 15,
    coordinate: 15,
  },
  colors: {
    headerBg: '1A237E',
    headerText: 'FFFFFF',
    alternateRow: 'F5F5F5',
    photoHighlight: 'E8F5E9',
    hyperlinkText: '0000FF',
  },
};

// ===== UTILITY CLASSES =====

/**
 * Utility class for data transformation operations
 */
class DataTransformationUtils {
  static formatTimezoneDate(date: Date | string, timezoneName: string): string {
    return dayjs(date).tz(timezoneName).format('YYYY-MM-DD HH:mm');
  }

  static transformActivityLogToDisplayData(activityLog: LogActivity): {
    leftColumnData: DocumentDisplayData[];
    rightColumnData: DocumentDisplayData[];
  } {
    const originalSubmittedTime = this.formatTimezoneDate(
      activityLog.original_submitted_time,
      activityLog.timezone_name,
    );

    const leftColumnData = [
      { label: 'Branch', value: activityLog.branch_name || activityLog.branch?.branch_name },
      { label: 'Role', value: activityLog.role_name || activityLog.role?.role_name },
      { label: 'User', value: activityLog.user_name || activityLog.user?.name },
      { label: 'Activity', value: activityLog.activity_name || activityLog.activity?.activity_name },
      { label: 'Device', value: activityLog.device_name || activityLog.device?.device_name },
    ];

    const rightColumnData = [
      { label: 'Timezone', value: activityLog.timezone_name || activityLog.timezone?.timezone_name },
      { label: 'Latitude', value: activityLog.latitude?.toString() },
      { label: 'Longitude', value: activityLog.longitude?.toString() },
      { label: 'Original Time', value: originalSubmittedTime },
    ];

    return { leftColumnData, rightColumnData };
  }

  static buildActivityLogDataRows(activityLog: LogActivity): string[][] {
    const originalSubmittedTime = this.formatTimezoneDate(
      activityLog.original_submitted_time,
      activityLog.timezone_name,
    );

    return [
      ['Branch', activityLog.branch_name || activityLog.branch?.branch_name || '-'],
      ['Role', activityLog.role_name || activityLog.role?.role_name || '-'],
      ['User', activityLog.user_name || activityLog.user?.name || '-'],
      ['Activity', activityLog.activity_name || activityLog.activity?.activity_name || '-'],
      ['Device', activityLog.device_name || activityLog.device?.device_name || '-'],
      ['Timezone', activityLog.timezone_name || activityLog.timezone?.timezone_name || '-'],
      ['Latitude', activityLog.latitude?.toString() || '-'],
      ['Longitude', activityLog.longitude?.toString() || '-'],
      ['Original Time', originalSubmittedTime],
      ['Comment', activityLog.comment || '-'],
    ];
  }
}

/**
 * Utility class for common document operations
 */
class DocumentUtils {
  static async loadImageFromUrl(url: string): Promise<Buffer | null> {
    try {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      return Buffer.from(response.data, 'binary');
    } catch (error) {
      console.error(`Error loading image from ${url}:`, error);
      return null;
    }
  }

  static generateFilename(prefix: string, extension: string): string {
    return `${prefix}-${dayjs().format('YYYYMMDD-HHmmss')}.${extension}`;
  }

  static createPdfDocument(): PDFKit.PDFDocument {
    return new PDFDocument({
      size: PDF_CONFIG.pageSize,
      autoFirstPage: false,
      margins: PDF_CONFIG.margins,
      info: {
        Title: 'Activity Log Report',
        Author: COMPANY_BRANDING.name,
        Creator: COMPANY_BRANDING.name,
      },
    });
  }
}

/**
 * Base class for PDF generation with common functionality
 */
abstract class BasePdfGenerator {
  protected setupDocument(): PdfDocumentSetup {
    const doc = DocumentUtils.createPdfDocument();
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));
    return { doc, buffers };
  }

  protected async finalizeDocument(
    doc: PDFKit.PDFDocument,
    buffers: Buffer[],
    prefix: string,
  ): Promise<DocumentGenerationResult> {
    doc.end();
    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer,
          filename: DocumentUtils.generateFilename(prefix, 'pdf'),
        });
      });
    });
  }

  protected drawSectionHeader(doc: PDFKit.PDFDocument, text: string, y: number): void {
    doc
      .fontSize(DOCUMENT_STYLING.fonts.sizes.header)
      .font('Helvetica-Bold')
      .fillColor(DOCUMENT_STYLING.colors.primary)
      .text(text, PDF_CONFIG.leftColumnX, y);

    doc
      .moveTo(PDF_CONFIG.leftColumnX, y + 25)
      .lineTo(545, y + 25)
      .lineWidth(1)
      .strokeColor(DOCUMENT_STYLING.colors.primary)
      .stroke();
  }

  protected addFieldRow(
    doc: PDFKit.PDFDocument,
    label: string,
    value: string,
    x: number,
    y: number,
    rowHeight: number = 25,
  ): number {
    const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
    const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

    doc
      .font('Helvetica-Bold')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor(DOCUMENT_STYLING.colors.secondary)
      .text(label, x, labelY);

    doc
      .font('Helvetica')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor(DOCUMENT_STYLING.colors.text)
      .text(value || '-', x + 90, valueY);

    return y + rowHeight;
  }

  protected async addCompanyHeader(doc: PDFKit.PDFDocument, timezone: Timezone): Promise<void> {
    const logoBuffer = await DocumentUtils.loadImageFromUrl(COMPANY_BRANDING.logoUrl);

    if (logoBuffer) {
      doc.image(logoBuffer, PDF_CONFIG.leftColumnX, 50, { fit: [50, 50] });
    } else {
      doc.save().translate(75, 75).rect(-25, -25, 50, 50).fill(DOCUMENT_STYLING.colors.primary);
    }

    doc
      .font('Helvetica-Bold')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.title)
      .fillColor(DOCUMENT_STYLING.colors.primary)
      .text(COMPANY_BRANDING.name, 110, 65);

    doc
      .font('Helvetica')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor(DOCUMENT_STYLING.colors.secondary)
      .text(
        `Generated: ${DataTransformationUtils.formatTimezoneDate(new Date(), timezone.timezone_name)}`,
        350,
        65,
      );
  }

  protected addFooter(doc: PDFKit.PDFDocument): void {
    const footerWidth = doc.widthOfString(COMPANY_BRANDING.footerText);
    doc
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor(DOCUMENT_STYLING.colors.footer)
      .text(
        COMPANY_BRANDING.footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );
  }
}

/**
 * Excel document generator with reusable methods
 */
class ExcelGenerator {
  async generateSingleActivityLog(
    activityLog: LogActivity,
    timezone: Timezone,
  ): Promise<DocumentGenerationResult> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Activity Log');

    let currentRow = 1;
    currentRow = this.addTitle(worksheet, 'UNIGUARD ACTIVITY LOG REPORT', 'G', currentRow);
    currentRow = this.addGenerationTime(worksheet, timezone, 'G', currentRow);
    currentRow = this.addUuid(worksheet, activityLog.uuid, 'G', currentRow);
    currentRow = this.addDataHeader(worksheet, ['Field', 'Value'], currentRow);

    const dataRows = DataTransformationUtils.buildActivityLogDataRows(activityLog);
    currentRow = this.addDataRows(worksheet, dataRows, currentRow);

    if (activityLog.photo_url) {
      currentRow = this.addPhotoUrl(worksheet, activityLog.photo_url, currentRow);
    }

    this.setColumnWidths(worksheet, [20, 50]);
    this.addFooter(worksheet, 'B', currentRow + 1);

    return this.finalizeWorkbook(workbook, `activity-log-${activityLog.id}`);
  }

  async generateMultipleActivityLogs(
    data: LogActivity[],
    filters: ActivityLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<DocumentGenerationResult> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Activity Logs');

    let currentRow = this.addMultipleLogsHeader(worksheet, data.length, timezone);
    currentRow = this.addFiltersSection(worksheet, filters, currentRow);
    currentRow = this.addDateRangeSection(worksheet, filters, currentRow);
    currentRow = this.addDataTableHeader(worksheet, currentRow);

    this.addActivityLogRows(worksheet, data, currentRow);
    this.setMultipleLogsColumnWidths(worksheet);
    this.addMultipleLogsFooter(worksheet, currentRow + data.length + 2);

    return this.finalizeWorkbook(workbook, 'activity-logs');
  }

  private addTitle(worksheet: ExcelJS.Worksheet, title: string, lastColumn: string, startRow: number): number {
    worksheet.mergeCells(`A${startRow}:${lastColumn}${startRow}`);
    const titleCell = worksheet.getCell(`A${startRow}`);
    titleCell.value = title;
    titleCell.font = { size: 16, bold: true, color: { argb: DOCUMENT_STYLING.colors.primary.replace('#', '') } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(startRow).height = 30;
    return startRow + 1;
  }

  private addGenerationTime(worksheet: ExcelJS.Worksheet, timezone: Timezone, lastColumn: string, currentRow: number): number {
    worksheet.mergeCells(`A${currentRow}:${lastColumn}${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${DataTransformationUtils.formatTimezoneDate(new Date(), timezone.timezone_name)}`;
    timeCell.font = { size: 10, color: { argb: DOCUMENT_STYLING.colors.secondary.replace('#', '') } };
    timeCell.alignment = { horizontal: 'center' };
    return currentRow + 1;
  }

  private addUuid(worksheet: ExcelJS.Worksheet, uuid: string, lastColumn: string, currentRow: number): number {
    worksheet.mergeCells(`A${currentRow}:${lastColumn}${currentRow}`);
    const uuidCell = worksheet.getCell(`A${currentRow}`);
    uuidCell.value = `UUID: ${uuid}`;
    uuidCell.font = { size: 10, color: { argb: DOCUMENT_STYLING.colors.secondary.replace('#', '') } };
    return currentRow + 1;
  }

  private addDataHeader(worksheet: ExcelJS.Worksheet, headers: string[], currentRow: number): number {
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    return currentRow + 1;
  }

  private addDataRows(worksheet: ExcelJS.Worksheet, dataRows: string[][], currentRow: number): number {
    dataRows.forEach((row, index) => {
      const excelRow = worksheet.addRow(row);
      if (index % 2 === 1) {
        excelRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: EXCEL_CONFIG.colors.alternateRow },
        };
      }
    });
    return currentRow + dataRows.length;
  }

  private addPhotoUrl(worksheet: ExcelJS.Worksheet, photoUrl: string, currentRow: number): number {
    const photoRow = worksheet.addRow([
      'Photo',
      {
        text: 'View Photo',
        hyperlink: photoUrl,
        tooltip: 'Click to open photo',
      }
    ]);

    photoRow.getCell(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: EXCEL_CONFIG.colors.photoHighlight },
    };

    photoRow.getCell(2).font = {
      color: { argb: EXCEL_CONFIG.colors.hyperlinkText },
      underline: true,
    };

    return currentRow + 1;
  }

  private setColumnWidths(worksheet: ExcelJS.Worksheet, widths: number[]): void {
    widths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });
  }

  private addFooter(worksheet: ExcelJS.Worksheet, lastColumn: string, currentRow: number): void {
    worksheet.mergeCells(`A${currentRow}:${lastColumn}${currentRow}`);
    const footerCell = worksheet.getCell(`A${currentRow}`);
    footerCell.value = COMPANY_BRANDING.footerText;
    footerCell.font = { size: 10, color: { argb: DOCUMENT_STYLING.colors.footer.replace('#', '') } };
    footerCell.alignment = { horizontal: 'center' };
  }

  private async finalizeWorkbook(workbook: ExcelJS.Workbook, prefix: string): Promise<DocumentGenerationResult> {
    const buffer = await workbook.xlsx.writeBuffer();
    return {
      buffer: buffer as Buffer,
      filename: DocumentUtils.generateFilename(prefix, 'xlsx'),
    };
  }

  private addMultipleLogsHeader(worksheet: ExcelJS.Worksheet, totalEntries: number, timezone: Timezone): number {
    let currentRow = 1;

    // Title
    worksheet.mergeCells('A1:K1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD ACTIVITY LOGS REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: DOCUMENT_STYLING.colors.primary.replace('#', '') } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // Generation time and total entries
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${DataTransformationUtils.formatTimezoneDate(new Date(), timezone.timezone_name)} | Total Entries: ${totalEntries}`;
    timeCell.font = { size: 10, color: { argb: DOCUMENT_STYLING.colors.secondary.replace('#', '') } };
    timeCell.alignment = { horizontal: 'center' };

    return currentRow + 1;
  }

  private addFiltersSection(worksheet: ExcelJS.Worksheet, filters: ActivityLogFiltersGenerateDocument, currentRow: number): number {
    // Filter criteria title
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: DOCUMENT_STYLING.colors.primary.replace('#', '') } };

    currentRow++;

    // Add filter details
    const filterMappings = [
      { condition: filters.startDate && filters.endDate, label: 'Date Range:', value: `${filters.startDate} to ${filters.endDate}` },
      { condition: filters.startTime && filters.endTime, label: 'Time Range:', value: `${filters.startTime} to ${filters.endTime}` },
      { condition: filters.branch, label: 'Branch:', value: filters.branch?.branch_name },
      { condition: filters.role, label: 'Role:', value: filters.role?.role_name },
      { condition: filters.user, label: 'User:', value: filters.user?.name },
      { condition: filters.user_labels.length > 0, label: 'User Labels:', value: filters.user_labels.map(l => l.label_name).join(', ') },
      { condition: filters.device, label: 'Device:', value: filters.device?.device_name },
      { condition: filters.device_labels.length > 0, label: 'Device Labels:', value: filters.device_labels.map(l => l.label_name).join(', ') },
      { condition: filters.activity, label: 'Activity:', value: filters.activity?.activity_name },
    ];

    filterMappings.forEach(filter => {
      if (filter.condition) {
        worksheet.getCell(`A${currentRow}`).value = filter.label;
        worksheet.getCell(`B${currentRow}`).value = filter.value;
        worksheet.getCell(`A${currentRow}`).font = { bold: true };
        currentRow++;
      }
    });

    return currentRow;
  }

  private addDateRangeSection(worksheet: ExcelJS.Worksheet, filters: ActivityLogFiltersGenerateDocument, currentRow: number): number {
    if (!filters.startDate || !filters.endDate) return currentRow;

    currentRow += 2;
    const startRow = currentRow;

    worksheet.getCell(`A${currentRow}`).value = 'Date Range:';
    worksheet.getCell(`A${currentRow}`).font = { bold: true };
    worksheet.getCell(`A${currentRow}`).alignment = { horizontal: 'left', vertical: 'middle' };

    const startDate = dayjs(filters.startDate);
    const endDate = dayjs(filters.endDate);
    const daysDiff = endDate.diff(startDate, 'day') + 1;
    const startTime = filters.startTime || '00:00';
    const endTime = filters.endTime || '23:59';

    const maxDaysToShow = 3;
    const daysToShow = Math.min(daysDiff, maxDaysToShow);
    let endRow = currentRow;

    for (let i = 0; i < daysToShow; i++) {
      if (i > 0) currentRow++;
      endRow = currentRow;

      const currentDate = startDate.add(i, 'day');
      const dateStr = currentDate.format('YYYY-MM-DD');
      worksheet.getCell(`B${currentRow}`).value = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
    }

    if (daysDiff > maxDaysToShow) {
      currentRow++;
      worksheet.getCell(`B${currentRow}`).value = '...';

      currentRow++;
      endRow = currentRow;
      const dateStr = endDate.format('YYYY-MM-DD');
      worksheet.getCell(`B${currentRow}`).value = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
    }

    worksheet.mergeCells(`A${startRow}:A${endRow}`);
    return currentRow + 1;
  }

  private addDataTableHeader(worksheet: ExcelJS.Worksheet, currentRow: number): number {
    const headers = [
      'UUID', 'Original Time', 'Branch', 'Role', 'User',
      'Activity', 'Device', 'Comment', 'Photo', 'Latitude', 'Longitude'
    ];

    currentRow += 2;
    worksheet.addRow(2);
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: EXCEL_CONFIG.colors.headerText } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: EXCEL_CONFIG.colors.headerBg },
    };
    headerRow.alignment = { horizontal: 'center' };

    return currentRow + 1;
  }

  private addActivityLogRows(worksheet: ExcelJS.Worksheet, data: LogActivity[], startRow: number): void {
    let currentRow = startRow;

    data.forEach(activityLog => {
      const originalSubmittedTime = DataTransformationUtils.formatTimezoneDate(
        activityLog.original_submitted_time,
        activityLog.timezone_name,
      );

      const rowData = [
        activityLog.uuid,
        originalSubmittedTime,
        activityLog.branch?.branch_name || '-',
        activityLog.role?.role_name || '-',
        activityLog.user?.name || '-',
        activityLog.activity?.activity_name || '-',
        activityLog.device?.device_name || '-',
        activityLog.comment || '-',
        activityLog.photo_url ? { text: 'View Photo', hyperlink: activityLog.photo_url, tooltip: 'Click to open photo' } : 'No photo',
        activityLog.latitude || '-',
        activityLog.longitude || '-',
      ];

      const row = worksheet.addRow(rowData);

      // Apply alternating row colors
      if (currentRow % 2 === 0) {
        for (let i = 1; i <= 11; i++) {
          if (i !== 9 || !activityLog.photo_url) {
            worksheet.getCell(currentRow, i).fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: EXCEL_CONFIG.colors.alternateRow },
            };
          }
        }
      }

      // Apply hyperlink styling
      if (activityLog.photo_url) {
        worksheet.getCell(currentRow, 9).font = {
          color: { argb: EXCEL_CONFIG.colors.hyperlinkText },
          underline: true,
        };
      }

      currentRow++;
    });
  }

  private setMultipleLogsColumnWidths(worksheet: ExcelJS.Worksheet): void {
    const widths = [
      EXCEL_CONFIG.columnWidths.uuid,
      EXCEL_CONFIG.columnWidths.time,
      EXCEL_CONFIG.columnWidths.standard,
      EXCEL_CONFIG.columnWidths.standard,
      EXCEL_CONFIG.columnWidths.standard,
      EXCEL_CONFIG.columnWidths.standard,
      EXCEL_CONFIG.columnWidths.standard,
      EXCEL_CONFIG.columnWidths.comment,
      EXCEL_CONFIG.columnWidths.photo,
      EXCEL_CONFIG.columnWidths.coordinate,
      EXCEL_CONFIG.columnWidths.coordinate,
    ];

    widths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });
  }

  private addMultipleLogsFooter(worksheet: ExcelJS.Worksheet, currentRow: number): void {
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const footerCell = worksheet.getCell(`A${currentRow}`);
    footerCell.value = COMPANY_BRANDING.footerText;
    footerCell.font = { size: 10, color: { argb: DOCUMENT_STYLING.colors.footer.replace('#', '') } };
    footerCell.alignment = { horizontal: 'center' };
  }
}

@Injectable()
export class ActivityLogGenerateDocumentService extends BasePdfGenerator {
  constructor() {
    super();
  }

  /**
   * Generates a PDF document for a single activity log entry
   */
  async generatePDFById(
    activityLog: LogActivity,
    timezone: Timezone,
  ): Promise<DocumentGenerationResult> {
    const { doc, buffers } = this.setupDocument();

    doc.addPage();
    await this.addCompanyHeader(doc, timezone);
    this.drawSectionHeader(doc, 'Activity Log Details', 120);
    this.addUuidSection(doc, activityLog.uuid, 155);

    let currentY = 190;
    const { leftColumnData, rightColumnData } = DataTransformationUtils.transformActivityLogToDisplayData(activityLog);
    currentY = this.renderDataTable(doc, leftColumnData, rightColumnData, currentY);
    currentY = this.addCommentSection(doc, activityLog.comment, currentY);
    currentY = await this.addPhotoSection(doc, activityLog.photo_url, currentY);

    this.addFooter(doc);
    return this.finalizeDocument(doc, buffers, 'activity-log');
  }

  /**
   * Generates a spreadsheet document for a single activity log entry
   */
  async generateSpreadsheetById(
    activityLog: LogActivity,
    timezone: Timezone,
  ): Promise<DocumentGenerationResult> {
    const excelGenerator = new ExcelGenerator();
    return excelGenerator.generateSingleActivityLog(activityLog, timezone);
  }

  /**
   * Generates a PDF document from multiple activity log entries
   */
  async generatePDF(
    data: LogActivity[],
    filters: ActivityLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<DocumentGenerationResult> {
    const { doc, buffers } = this.setupDocument();

    await this.addCoverPage(doc, 'ACTIVITY LOG REPORT');
    this.addFiltersSection(doc, filters, timezone, data.length, 300);
    this.addFooter(doc);

    for (let index = 0; index < data.length; index++) {
      await this.addActivityLogPage(doc, data[index], index, data.length);
    }

    return this.finalizeDocument(doc, buffers, 'activity-log');
  }

  /**
   * Generates a spreadsheet document from multiple activity log entries
   */
  async generateSpreadsheet(
    data: LogActivity[],
    filters: ActivityLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<DocumentGenerationResult> {
    const excelGenerator = new ExcelGenerator();
    return excelGenerator.generateMultipleActivityLogs(data, filters, timezone);
  }

  // ===== PRIVATE HELPER METHODS =====

  private addUuidSection(doc: PDFKit.PDFDocument, uuid: string, y: number): void {
    doc
      .font('Helvetica')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor(DOCUMENT_STYLING.colors.secondary)
      .text(`UUID: ${uuid}`, PDF_CONFIG.leftColumnX, y);

    doc
      .moveTo(PDF_CONFIG.leftColumnX, y + 15)
      .lineTo(545, y + 15)
      .lineWidth(0.5)
      .strokeColor(DOCUMENT_STYLING.colors.border)
      .stroke();
  }

  private renderDataTable(
    doc: PDFKit.PDFDocument,
    leftColumnData: DocumentDisplayData[],
    rightColumnData: DocumentDisplayData[],
    startY: number,
  ): number {
    let currentY = startY;
    const rowHeight = PDF_CONFIG.rowHeight;

    leftColumnData.forEach((item, i) => {
      if (i % 2 === 0) {
        doc.fillColor(DOCUMENT_STYLING.colors.lightGray).rect(PDF_CONFIG.leftColumnX, currentY, PDF_CONFIG.columnWidth, rowHeight).fill();
      }

      currentY = this.addFieldRow(doc, item.label + ':', item.value, PDF_CONFIG.leftColumnX, currentY, rowHeight);

      if (i < rightColumnData.length) {
        this.addFieldRow(doc, rightColumnData[i].label + ':', rightColumnData[i].value, PDF_CONFIG.rightColumnX, currentY - rowHeight, rowHeight);
      }
    });

    return currentY;
  }

  private addDynamicFieldRow(
    doc: PDFKit.PDFDocument,
    label: string,
    value: string,
    x: number,
    y: number,
    maxWidth: number = PDF_CONFIG.columnWidth,
  ): number {
    doc.font('Helvetica-Bold').fontSize(DOCUMENT_STYLING.fonts.sizes.normal);
    const labelWidth = doc.widthOfString(label);
    const dynamicLabelWidth = Math.max(labelWidth + 10, 90);
    const availableValueWidth = maxWidth - dynamicLabelWidth - 20;

    doc.font('Helvetica').fontSize(DOCUMENT_STYLING.fonts.sizes.normal);
    const valueText = value || '-';
    const valueHeight = doc.heightOfString(valueText, {
      width: availableValueWidth,
      lineBreak: true
    });

    const dynamicRowHeight = Math.max(25, valueHeight + 10);
    const labelY = y + (dynamicRowHeight - doc.currentLineHeight()) / 2;
    const valueY = y + 5;

    doc
      .font('Helvetica-Bold')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor(DOCUMENT_STYLING.colors.secondary)
      .text(label, x, labelY);

    doc
      .font('Helvetica')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor(DOCUMENT_STYLING.colors.text)
      .text(valueText, x + dynamicLabelWidth, valueY, {
        width: availableValueWidth,
        ellipsis: false,
        lineBreak: true
      });

    return y + dynamicRowHeight;
  }

  private addCommentSection(doc: PDFKit.PDFDocument, comment: string, startY: number): number {
    let currentY = startY + 20;
    this.drawSectionHeader(doc, 'Additional Information', currentY);
    currentY += 40;
    currentY = this.addDynamicFieldRow(doc, 'Comment:', comment || '-', PDF_CONFIG.leftColumnX, currentY, PDF_CONFIG.columnWidth);
    return currentY + 10;
  }

  private async addPhotoSection(doc: PDFKit.PDFDocument, photoUrl: string, startY: number): Promise<number> {
    if (!photoUrl) return startY;

    let currentY = startY + 20;
    this.drawSectionHeader(doc, 'Activity Photo', currentY);
    currentY += 40;

    try {
      const imageBuffer = await DocumentUtils.loadImageFromUrl(photoUrl);
      if (imageBuffer) {
        doc.image(imageBuffer, PDF_CONFIG.leftColumnX, currentY, {
          fit: [200, 150],
          link: photoUrl,
        });
        doc
          .font('Helvetica')
          .fontSize(DOCUMENT_STYLING.fonts.sizes.small)
          .fillColor('#666666')
          .text('(Click image to view full size)', PDF_CONFIG.leftColumnX, currentY + 155);
        return currentY + 175;
      }
    } catch (error) {
      console.error('Error loading photo:', error);
    }

    // Fallback for failed image loading
    doc
      .font('Helvetica')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor('#0066CC')
      .text('Photo: Click here to view', PDF_CONFIG.leftColumnX, currentY, {
        link: photoUrl,
        underline: true,
      });

    currentY = this.addDynamicFieldRow(doc, 'Status:', 'Image could not be loaded - click link above to view', PDF_CONFIG.leftColumnX, currentY + 20, PDF_CONFIG.columnWidth);
    return currentY + 10;
  }

  private async addCoverPage(doc: PDFKit.PDFDocument, title: string): Promise<void> {
    doc.addPage();
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    const logoBuffer = await DocumentUtils.loadImageFromUrl(COMPANY_BRANDING.logoUrl);
    if (logoBuffer) {
      doc.image(logoBuffer, (doc.page.width - 80) / 2, 100, { fit: [80, 80] });
    } else {
      doc
        .save()
        .translate(doc.page.width / 2, 140)
        .rect(-40, -40, 80, 80)
        .fill(DOCUMENT_STYLING.colors.primary);
    }

    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor(DOCUMENT_STYLING.colors.primary)
      .text(COMPANY_BRANDING.name, (doc.page.width - doc.widthOfString(COMPANY_BRANDING.name)) / 2, 190);

    doc.font('Helvetica-Bold').fontSize(18).fillColor(DOCUMENT_STYLING.colors.primary);
    const titleWidth = doc.widthOfString(title);
    doc.text(title, (doc.page.width - titleWidth) / 2, 240);
  }

  private addFiltersSection(
    doc: PDFKit.PDFDocument,
    filters: ActivityLogFiltersGenerateDocument,
    timezone: Timezone,
    totalEntries: number,
    startY: number,
  ): number {
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor(DOCUMENT_STYLING.colors.primary)
      .text('Active Filters', PDF_CONFIG.leftColumnX, startY);

    doc
      .moveTo(PDF_CONFIG.leftColumnX, startY + 25)
      .lineTo(545, startY + 25)
      .lineWidth(1)
      .strokeColor(DOCUMENT_STYLING.colors.primary)
      .stroke();

    let currentY = startY + 30;
    const leftFilters = this.buildLeftFilters(filters);
    const rightFilters = this.buildRightFilters(filters);
    const maxRows = Math.max(leftFilters.length, rightFilters.length);

    for (let i = 0; i < maxRows; i++) {
      if (i < leftFilters.length) {
        this.addFieldRow(doc, leftFilters[i].label, leftFilters[i].value, PDF_CONFIG.leftColumnX, currentY, 25);
      }
      if (i < rightFilters.length) {
        this.addFieldRow(doc, rightFilters[i].label, rightFilters[i].value, PDF_CONFIG.rightColumnX, currentY, 25);
      }
      currentY += 25;
    }

    doc.font('Helvetica').fontSize(12).fillColor(DOCUMENT_STYLING.colors.secondary);
    currentY = this.addFieldRow(doc, 'Report Generated:', DataTransformationUtils.formatTimezoneDate(new Date(), timezone.timezone_name), PDF_CONFIG.leftColumnX, currentY);
    currentY = this.addFieldRow(doc, 'Total Entries:', totalEntries.toString(), PDF_CONFIG.leftColumnX, currentY);

    if (filters.startDate && filters.endDate) {
      currentY = this.addDateRangeExplanation(doc, filters, currentY);
    }

    return currentY;
  }

  private buildLeftFilters(filters: ActivityLogFiltersGenerateDocument): { label: string; value: string }[] {
    return [
      { label: 'Start Date:', value: filters.startDate ? dayjs(filters.startDate).format('MMMM D, YYYY') : 'All Dates' },
      { label: 'End Date:', value: filters.endDate ? dayjs(filters.endDate).format('MMMM D, YYYY') : 'All Dates' },
      { label: 'Branch:', value: filters.branch ? filters.branch.branch_name : 'All Branches' },
      { label: 'User:', value: filters.user ? filters.user.name : 'All Users' },
      { label: 'Device:', value: filters.device ? filters.device.device_name : 'All Devices' },
      { label: 'Activity:', value: filters.activity ? filters.activity.activity_name : 'All Activities' },
    ];
  }

  private buildRightFilters(filters: ActivityLogFiltersGenerateDocument): { label: string; value: string }[] {
    return [
      { label: 'Start Time:', value: filters.startTime || 'All Times' },
      { label: 'End Time:', value: filters.endTime || 'All Times' },
      { label: 'Role:', value: filters.role ? filters.role.role_name : 'All Roles' },
      { label: 'User Labels:', value: filters.user_labels?.length > 0 ? filters.user_labels.map(label => label.label_name).join(', ') : 'All Labels' },
      { label: 'Device Labels:', value: filters.device_labels?.length > 0 ? filters.device_labels.map(label => label.label_name).join(', ') : 'All Labels' },
    ];
  }

  private addDateRangeExplanation(
    doc: PDFKit.PDFDocument,
    filters: ActivityLogFiltersGenerateDocument,
    startY: number,
  ): number {
    let currentY = startY + 20;

    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor(DOCUMENT_STYLING.colors.primary)
      .text('Data From:', PDF_CONFIG.leftColumnX, currentY);

    currentY += 25;

    const startDate = dayjs(filters.startDate);
    const endDate = dayjs(filters.endDate);
    const daysDiff = endDate.diff(startDate, 'day') + 1;
    const startTime = filters.startTime || '00:00';
    const endTime = filters.endTime || '23:59';

    const maxDaysToShow = 3;
    const daysToShow = Math.min(daysDiff, maxDaysToShow);

    for (let i = 0; i < daysToShow; i++) {
      const currentDate = startDate.add(i, 'day');
      const dateStr = currentDate.format('YYYY-MM-DD');
      const timeRangeStr = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;

      doc
        .font('Helvetica')
        .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
        .fillColor(DOCUMENT_STYLING.colors.text)
        .text(timeRangeStr, PDF_CONFIG.leftColumnX, currentY + i * 20);
    }

    if (daysDiff > maxDaysToShow) {
      doc
        .font('Helvetica')
        .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
        .fillColor(DOCUMENT_STYLING.colors.text)
        .text('...', PDF_CONFIG.leftColumnX, currentY + maxDaysToShow * 20);

      const lastDate = endDate.format('YYYY-MM-DD');
      const lastTimeRangeStr = `${lastDate} ${startTime} until ${lastDate} ${endTime}`;

      doc
        .font('Helvetica')
        .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
        .fillColor(DOCUMENT_STYLING.colors.text)
        .text(lastTimeRangeStr, PDF_CONFIG.leftColumnX, currentY + (maxDaysToShow + 1) * 20);

      return currentY + (maxDaysToShow + 2) * 20;
    }

    return currentY + daysToShow * 20;
  }

  private async addActivityLogPage(
    doc: PDFKit.PDFDocument,
    log: LogActivity,
    index: number,
    totalEntries: number,
  ): Promise<void> {
    doc.addPage();

    this.drawSectionHeader(doc, `Activity Log Entry #${index + 1}`, 50);
    this.addUuidSection(doc, log.uuid, 85);

    let currentY = 120;
    const { leftColumnData, rightColumnData } = DataTransformationUtils.transformActivityLogToDisplayData(log);
    currentY = this.renderDataTable(doc, leftColumnData, rightColumnData, currentY);
    currentY = this.addCommentSection(doc, log.comment, currentY);
    await this.addPhotoSection(doc, log.photo_url, currentY);

    this.addPageNumber(doc, index + 2, totalEntries + 1);
  }

  private addPageNumber(doc: PDFKit.PDFDocument, currentPage: number, totalPages: number): void {
    const pageText = `Page ${currentPage} of ${totalPages}`;
    const pageWidth = doc.widthOfString(pageText);
    doc
      .font('Helvetica')
      .fontSize(DOCUMENT_STYLING.fonts.sizes.normal)
      .fillColor(DOCUMENT_STYLING.colors.footer)
      .text(pageText, (doc.page.width - pageWidth) / 2, doc.page.height - 30);
  }
}
