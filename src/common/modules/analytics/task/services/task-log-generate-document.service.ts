import { Injectable } from '@nestjs/common';
import { Branch } from '../../../database/entities/branch.entity';
import { User } from '../../../database/entities/user.entity';
import { Role } from '../../../database/entities/role.entity';
import { Label } from '../../../database/entities/label.entity';
import { Device } from '../../../database/entities/device.entity';
import axios from 'axios';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import { Task } from '../../../database/entities/task.entity';
import { LogTask } from '../../../database/entities/log-task.entity';
import { LogTaskField } from '../../../database/entities/log-task-field.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Timezone } from '../../../database/entities/timezone.entity';

dayjs.extend(timezone);

export interface TaskLogFilters {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  role: Role | null;
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
  task: Task | null;
}

@Injectable()
export class TaskLogGenerateDocumentService {
  // Constants
  private readonly LOGO_URL = 'https://trial-serverless.web.app/logo.png';
  private readonly COMPANY_NAME = 'UNIGUARD';
  private readonly SYSTEM_NAME = 'UniGuard Security System';
  private readonly PRIMARY_COLOR = '#1a237e';
  private readonly SECONDARY_COLOR = '#546e7a';
  private readonly TEXT_COLOR = '#263238';
  private readonly LIGHT_GRAY = '#fafafa';
  private readonly BORDER_COLOR = '#e0e0e0';

  constructor(
    @InjectRepository(LogTaskField)
    private taskFieldRepository: Repository<LogTaskField>,
  ) {}

  // Common PDF helper methods
  private createPdfDoc = () => {
    return new PDFDocument({
      size: 'A4',
      autoFirstPage: false,
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      info: {
        Title: 'Task Log Report',
        Author: this.SYSTEM_NAME,
        Creator: this.COMPANY_NAME,
      },
    });
  };

  private drawSectionHeader = (doc: PDFKit.PDFDocument, text: string, y: number) => {
    doc
      .fontSize(16)
      .font('Helvetica-Bold')
      .fillColor(this.PRIMARY_COLOR)
      .text(text, 50, y);

    doc
      .moveTo(50, y + 25)
      .lineTo(545, y + 25)
      .lineWidth(1)
      .strokeColor(this.PRIMARY_COLOR)
      .stroke();
  };

  private addFieldRow = (
    doc: PDFKit.PDFDocument,
    label: string,
    value: string,
    x: number,
    y: number,
    rowHeight: number = 25,
  ) => {
    const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
    const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

    doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(label, x, labelY);

    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.TEXT_COLOR)
      .text(value || '-', x + 90, valueY, {
        width: 200,
        ellipsis: false,
        lineBreak: true
      });

    return y + rowHeight;
  };

  // Method baru untuk menangani field dengan ukuran dinamis
  private addDynamicFieldRow = (
    doc: PDFKit.PDFDocument,
    label: string,
    value: string,
    x: number,
    y: number,
    maxWidth: number = 495,
  ) => {
    // Set font untuk label dan hitung lebar
    doc.font('Helvetica-Bold').fontSize(10);
    const labelWidth = doc.widthOfString(label);
    const labelPadding = 10;
    const dynamicLabelWidth = Math.max(labelWidth + labelPadding, 90);
    
    // Hitung lebar yang tersedia untuk value
    const availableValueWidth = maxWidth - dynamicLabelWidth - 20; // 20 untuk margin
    
    // Set font untuk value dan hitung tinggi yang dibutuhkan
    doc.font('Helvetica').fontSize(10);
    const valueText = value || '-';
    const valueHeight = doc.heightOfString(valueText, {
      width: availableValueWidth,
      lineBreak: true
    });
    
    // Hitung tinggi row berdasarkan konten terbesar
    const minRowHeight = 25;
    const dynamicRowHeight = Math.max(minRowHeight, valueHeight + 10); // 10 untuk padding
    
    // Gambar background untuk row
    const rowRect = {
      x: x,
      y: y,
      width: maxWidth,
      height: dynamicRowHeight
    };
    
    // Hitung posisi vertikal untuk center alignment
    const labelY = y + (dynamicRowHeight - doc.currentLineHeight()) / 2;
    const valueY = y + 5; // Padding atas untuk value
    
    // Render label
    doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(label, x, labelY);
    
    // Render value dengan word wrap
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.TEXT_COLOR)
      .text(valueText, x + dynamicLabelWidth, valueY, {
        width: availableValueWidth,
        ellipsis: false,
        lineBreak: true
      });
    
    return y + dynamicRowHeight;
  };

  private async loadLogo(): Promise<Buffer | null> {
    try {
      const response = await axios.get(this.LOGO_URL, {
        responseType: 'arraybuffer',
      });
      return Buffer.from(response.data, 'binary');
    } catch (error) {
      console.error('Error loading logo:', error);
      return null;
    }
  }

  private formatTimezone(date: string, timezoneName: string): string {
    return dayjs(date).tz(timezoneName).format('YYYY-MM-DD HH:mm');
  }

  private generateFilename(prefix: string, timezone: Timezone, extension: string): string {
    const timestamp = dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss');
    return `${prefix}-${timestamp}.${extension}`;
  }

  // Common data mapping methods
  private getTaskLogColumnData(log: LogTask) {
    const originalSubmittedTime = dayjs(log.original_submitted_time).tz(log.timezone_name);

    const leftColumnData = [
      { label: 'Role', value: log.role?.role_name || log.role_name },
      { label: 'User', value: log.user?.name || log.user_name },
      { label: 'Task', value: log.task?.task_name || log.task_name },
      { label: 'Device', value: log.device_name },
      { label: 'Branch', value: log.branch?.branch_name || log.branch_name },
      { label: 'Task Type', value: log.task_type },
    ];

    const rightColumnData = [
      { label: 'Timezone', value: log.timezone_name },
      { label: 'Latitude', value: log.latitude?.toString() },
      { label: 'Longitude', value: log.longitude?.toString() },
      {
        label: 'Original Time',
        value: originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      },
    ];

    return { leftColumnData, rightColumnData, originalSubmittedTime };
  }

  private getFilterDisplayData(filters: TaskLogFilters) {
    const leftFilters = [
      {
        key: 'startDate',
        label: 'Start Date:',
        valueFunc: () =>
          filters.startDate
            ? `${dayjs(filters.startDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'endDate',
        label: 'End Date:',
        valueFunc: () =>
          filters.endDate
            ? `${dayjs(filters.endDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'user',
        label: 'User:',
        valueFunc: () => (filters.user ? `${filters.user.name}` : 'All Users'),
      },
      {
        key: 'device',
        label: 'Device:',
        valueFunc: () =>
          filters.device ? `${filters.device.device_name}` : 'All Devices',
      },
      {
        key: 'task',
        label: 'Task:',
        valueFunc: () =>
          filters.task ? `${filters.task.task_name}` : 'All Tasks',
      },
    ];

    const rightFilters = [
      {
        key: 'startTime',
        label: 'Start Time:',
        valueFunc: () =>
          filters.startTime ? `${filters.startTime}` : 'All Times',
      },
      {
        key: 'endTime',
        label: 'End Time:',
        valueFunc: () => (filters.endTime ? `${filters.endTime}` : 'All Times'),
      },
      {
        key: 'role',
        label: 'Role:',
        valueFunc: () =>
          filters.role ? `${filters.role.role_name}` : 'All Roles',
      },
      {
        key: 'user_labels',
        label: 'User Labels:',
        valueFunc: () =>
          filters.user_labels && filters.user_labels.length > 0
            ? filters.user_labels.map(label => label.label_name).join(', ')
            : 'All Labels',
      },
      {
        key: 'device_labels',
        label: 'Device Labels:',
        valueFunc: () =>
          filters.device_labels && filters.device_labels.length > 0
            ? filters.device_labels.map(label => label.label_name).join(', ')
            : 'All Labels',
      },
    ];

    return { leftFilters, rightFilters };
  }

  // Common PDF methods
  private async addLogoAndCompanyName(doc: PDFKit.PDFDocument, x: number, y: number, logoSize: number = 80) {
    const logoBuffer = await this.loadLogo();

    if (logoBuffer) {
      doc.image(logoBuffer, x, y, {
        fit: [logoSize, logoSize],
      });
    } else {
      // Fallback if logo loading fails
      doc
        .save()
        .translate(x + logoSize / 2, y + logoSize / 2)
        .rect(-logoSize / 2, -logoSize / 2, logoSize, logoSize)
        .fill(this.PRIMARY_COLOR);
    }

    // Add company name
    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor(this.PRIMARY_COLOR)
      .text(
        this.COMPANY_NAME,
        (doc.page.width - doc.widthOfString(this.COMPANY_NAME)) / 2,
        y + logoSize + 10,
      );
  }

  private async processTaskFieldsForPDF(
    doc: PDFKit.PDFDocument,
    logTaskId: number,
    currentY: number
  ): Promise<number> {
    const taskFields = await this.taskFieldRepository.find({
      where: { log_task_id: logTaskId },
      relations: ['field_type'],
    });

    for (const [fieldIndex, field] of taskFields.entries()) {
      // Handle image and signature fields differently
      if (
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
      ) {
        // Gambar background untuk field image/signature
        if (fieldIndex % 2 === 0) {
          doc.fillColor(this.LIGHT_GRAY).rect(50, currentY, 495, 30).fill();
        }

        // Add field name
        doc
          .font('Helvetica-Bold')
          .fontSize(10)
          .fillColor(this.SECONDARY_COLOR)
          .text(
            field.task_field_name + ':',
            50,
            currentY + (30 - doc.currentLineHeight()) / 2,
          );

        currentY += 30; // Space after field name

        try {
          // Try to load and add the image
          const response = await axios.get(field.field_type_value, {
            responseType: 'arraybuffer',
          });
          const imageBuffer = Buffer.from(response.data, 'binary');

          // Add the image with reasonable dimensions
          doc.image(imageBuffer, 50, currentY, {
            fit: [200, 100],
            align: 'center',
          });

          // Add extra space after image
          currentY += 110; // 100 for image + 10 padding
        } catch (error) {
          // If image loading fails, show error message
          doc
            .font('Helvetica')
            .fontSize(10)
            .fillColor('#f44336')
            .text('Failed to load image', 50, currentY);
          currentY += 25;
        }
      } else {
        // Regular field handling dengan dynamic sizing
        // Gambar background untuk alternating rows
        if (fieldIndex % 2 === 0) {
          // Hitung tinggi yang dibutuhkan terlebih dahulu untuk background
          doc.font('Helvetica').fontSize(10);
          const valueText = field.field_type_value || '-';
          const availableValueWidth = 495 - Math.max(doc.widthOfString(field.task_field_name + ':') + 10, 90) - 20;
          const valueHeight = doc.heightOfString(valueText, {
            width: availableValueWidth,
            lineBreak: true
          });
          const dynamicRowHeight = Math.max(25, valueHeight + 10);
          
          doc.fillColor(this.LIGHT_GRAY).rect(50, currentY, 495, dynamicRowHeight).fill();
        }

        // Gunakan method dinamis untuk field biasa
        currentY = this.addDynamicFieldRow(
          doc,
          field.task_field_name + ':',
          field.field_type_value,
          50,
          currentY,
          495,
        );
      }
    }

    return currentY;
  }

  // Common Excel helper methods
  private addExcelTitle(worksheet: ExcelJS.Worksheet, title: string, columnSpan: string): number {
    let currentRow = 1;
    worksheet.mergeCells(columnSpan);
    const titleCell = worksheet.getCell('A1');
    titleCell.value = title;
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    return currentRow;
  }

  private addExcelGenerationInfo(
    worksheet: ExcelJS.Worksheet,
    timezone: Timezone,
    currentRow: number,
    columnSpan: string,
    additionalInfo?: string
  ): number {
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:${columnSpan.split(':')[1]}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    const generationTime = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`;
    timeCell.value = additionalInfo ? `${generationTime} | ${additionalInfo}` : generationTime;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };
    return currentRow;
  }

  private addExcelHeaderRow(worksheet: ExcelJS.Worksheet, headers: string[]): ExcelJS.Row {
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '1A237E' },
    };
    headerRow.alignment = { horizontal: 'center' };
    return headerRow;
  }

  private addExcelAlternatingRow(worksheet: ExcelJS.Worksheet, data: any[], index: number): ExcelJS.Row {
    const row = worksheet.addRow(data);
    if (index % 2 === 0) {
      row.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'F5F5F5' },
      };
    }
    return row;
  }

  private addExcelFooter(worksheet: ExcelJS.Worksheet, currentRow: number, columnSpan: string): void {
    currentRow += 2;
    
    // Parse columnSpan to get the end column
    let endColumn: string;
    if (columnSpan.includes(':')) {
      endColumn = columnSpan.split(':')[1];
    } else {
      // If columnSpan is just a single column like 'G', use it directly
      endColumn = columnSpan;
    }
    
    const mergeRange = `A${currentRow}:${endColumn}${currentRow}`;
    
    try {
      // Check if cells are already merged by trying to get the merged cell range
      const cell = worksheet.getCell(`A${currentRow}`);
      if (!cell.isMerged) {
        worksheet.mergeCells(mergeRange);
      }
    } catch (error) {
      // If merge fails, try to set the value directly without merging
      console.warn('Could not merge cells for footer, setting value directly:', error.message);
    }
    
    const footerCell = worksheet.getCell(`A${currentRow}`);
    footerCell.value = this.SYSTEM_NAME;
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };
  }

  async generatePDF(
    data: LogTask[],
    filters: TaskLogFilters,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Add cover page
    doc.addPage();

    // Add background
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    // Add company logo and name
    await this.addLogoAndCompanyName(doc, (doc.page.width - 80) / 2, 100, 80);

    // Add title
    doc.font('Helvetica-Bold').fontSize(18).fillColor(this.PRIMARY_COLOR);
    const titleWidth = doc.widthOfString('TASK LOG REPORT');
    doc.text('TASK LOG REPORT', (doc.page.width - titleWidth) / 2, 240);

    // Moved metadata section up (from 350 to 300)
    const metadataY = 300;
    doc.font('Helvetica').fontSize(12).fillColor(this.SECONDARY_COLOR);

    // Add active filters section
    this.drawSectionHeader(doc, 'Active Filters', metadataY);
    let filterY = metadataY + 30;

    // Get filter display data
    const { leftFilters, rightFilters } = this.getFilterDisplayData(filters);

    // Render filters in two columns
    const rowHeight = 25;
    const leftX = 50;
    const rightX = 300;

    const maxRows = Math.max(leftFilters.length, rightFilters.length);
    for (let i = 0; i < maxRows; i++) {
      // Add left column filter
      if (i < leftFilters.length) {
        const leftFilter = leftFilters[i];
        this.addFieldRow(
          doc,
          leftFilter.label,
          leftFilter.valueFunc(),
          leftX,
          filterY,
          rowHeight,
        );
      }

      // Add right column filter
      if (i < rightFilters.length) {
        const rightFilter = rightFilters[i];
        this.addFieldRow(
          doc,
          rightFilter.label,
          rightFilter.valueFunc(),
          rightX,
          filterY,
          rowHeight,
        );
      }

      filterY += rowHeight;
    }

    // Add generation info
    doc.font('Helvetica').fontSize(12).fillColor(this.SECONDARY_COLOR);

    filterY = this.addFieldRow(
      doc,
      'Report Generated:',
      dayjs().tz(timezone.timezone_name).format('MMMM D, YYYY HH:mm'),
      50,
      filterY,
    );
    filterY = this.addFieldRow(
      doc,
      'Total Entries:',
      data.length.toString(),
      50,
      filterY,
    );

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      filterY += 20;
      doc
        .font('Helvetica-Bold')
        .fontSize(14)
        .fillColor(this.PRIMARY_COLOR)
        .text('Data From:', 50, filterY);

      filterY += 25;

      // Calculate all days between start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      // Display up to 5 days with truncation if more
      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        const timeRangeStr = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor(this.TEXT_COLOR)
          .text(timeRangeStr, 50, filterY + i * 20);
      }

      // Add truncation indicator if there are more days
      if (daysDiff > maxDaysToShow) {
        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor(this.TEXT_COLOR)
          .text('...', 50, filterY + maxDaysToShow * 20);

        // Show the last day
        const lastDate = endDate.format('YYYY-MM-DD');
        const lastTimeRangeStr = `${lastDate} ${startTime} until ${lastDate} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor(this.TEXT_COLOR)
          .text(lastTimeRangeStr, 50, filterY + (maxDaysToShow + 1) * 20);

        filterY += (maxDaysToShow + 2) * 20;
      } else {
        filterY += daysToShow * 20;
      }
    }

    // Add footer to cover page
    const footerWidth = doc.widthOfString(this.SYSTEM_NAME);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        this.SYSTEM_NAME,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 100,
      );

    // Process each task log entry
    for (const [index, log] of data.entries()) {
      doc.addPage();

      // Add page header with UUID
      this.drawSectionHeader(doc, `Task Log Entry #${index + 1}`, 50);

      // Add UUID right after header
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor(this.SECONDARY_COLOR)
        .text(`UUID: ${log.uuid}`, 50, 85);

      // Add horizontal line after UUID
      doc
        .moveTo(50, 100)
        .lineTo(545, 100)
        .lineWidth(0.5)
        .strokeColor(this.BORDER_COLOR)
        .stroke();

      // Add entry details with improved layout
      let currentY = 120;

      // Get column data for this log
      const { leftColumnData, rightColumnData } = this.getTaskLogColumnData(log);

      // Draw table content with alternating background and vertical centering
      leftColumnData.forEach((item, i) => {
        const rowHeight = 30; // Increased row height for better spacing

        // Draw alternating background
        if (i % 2 === 0) {
          doc.fillColor(this.LIGHT_GRAY).rect(50, currentY, 495, rowHeight).fill();
        }

        // Draw left column data
        currentY = this.addFieldRow(
          doc,
          item.label + ':',
          item.value,
          50,
          currentY,
          rowHeight,
        );

        // Draw right column data if available
        if (i < rightColumnData.length) {
          this.addFieldRow(
            doc,
            rightColumnData[i].label + ':',
            rightColumnData[i].value,
            300,
            currentY - rowHeight,
            rowHeight,
          );
        }
      });

      // Add task fields section
      currentY += 20;
      this.drawSectionHeader(doc, 'Task Fields', currentY);
      currentY += 40;

      // Process task fields using helper method
      currentY = await this.processTaskFieldsForPDF(doc, log.id, currentY);

      // Add page number at fixed position from bottom
      const pageText = `Page ${index + 2} of ${data.length + 1}`;
      const pageWidth = doc.widthOfString(pageText);
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#9e9e9e')
        .text(pageText, (doc.page.width - pageWidth) / 2, doc.page.height - 30);
    }

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: this.generateFilename('task-logs', timezone, 'pdf'),
        });
      });
    });
  }

  async generateSpreadsheet(
    data: LogTask[],
    filters: TaskLogFilters,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task Logs');

    // Add title and styling
    let currentRow = this.addExcelTitle(worksheet, `${this.COMPANY_NAME} TASK LOGS REPORT`, 'A1:K1');

    // Add generation time and total entries
    currentRow = this.addExcelGenerationInfo(
      worksheet,
      timezone,
      currentRow,
      'A2:K2',
      `Total Entries: ${data.length}`
    );

    // Add filter information
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: '1A237E' } };

    // Add filter details using helper method
    const filterData = [
      { condition: filters.startDate && filters.endDate, label: 'Date Range:', value: `${filters.startDate} to ${filters.endDate}` },
      { condition: filters.startTime && filters.endTime, label: 'Time Range:', value: `${filters.startTime} to ${filters.endTime}` },
      { condition: filters.role, label: 'Role:', value: filters.role?.role_name },
      { condition: filters.user, label: 'User:', value: filters.user?.name },
      { condition: !filters.user && filters.user_labels.length > 0, label: 'User Labels:', value: filters.user_labels.map(label => label.label_name).join(', ') },
      { condition: filters.device, label: 'Device:', value: filters.device?.device_name },
      { condition: !filters.device && filters.device_labels.length > 0, label: 'Device Labels:', value: filters.device_labels.map(label => label.label_name).join(', ') },
      { condition: filters.task, label: 'Task:', value: filters.task?.task_name },
    ];

    filterData.forEach(filter => {
      if (filter.condition) {
        currentRow++;
        worksheet.getCell(`A${currentRow}`).value = filter.label;
        worksheet.getCell(`B${currentRow}`).value = filter.value;
        worksheet.getCell(`A${currentRow}`).font = { bold: true };
      }
    });

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      currentRow += 2;
      let startRow = currentRow;
      let endRow = currentRow;
      const cellDateRange = worksheet.getCell(`A${currentRow}`);
      cellDateRange.value = 'Date Range:';
      cellDateRange.font = { bold: true };
      cellDateRange.alignment = { horizontal: 'center', vertical: 'middle' };

      // Calculate the number of days between the start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        if (i > 0) {
          currentRow++;
        }
        endRow = currentRow;

        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      if (daysDiff > maxDaysToShow) {
        currentRow++;
        worksheet.getCell(`B${currentRow}`).value = '...';

        // Show last day
        currentRow++;
        endRow = currentRow;
        const dateStr = endDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      // Merge the date range cells
      worksheet.mergeCells(`A${startRow}:A${endRow}`);
    }

    // Add main data section header
    currentRow += 2;
    const mainDataHeaderRow = worksheet.addRow(['Task Logs Data']);
    mainDataHeaderRow.font = {
      size: 14,
      bold: true,
      color: { argb: '1A237E' },
    };
    currentRow++;

    // Add table headers for basic info
    const headers = [
      'UUID',
      'Role',
      'User',
      'Task',
      'Device',
      'Branch',
      'Task Type',
      'Timezone',
      'Latitude',
      'Longitude',
      'Original Time',
    ];

    // Add header row with styling using helper method
    this.addExcelHeaderRow(worksheet, headers);
    currentRow++;

    // Process each task log
    for (const [index, log] of data.entries()) {
      // Get column data for this log
      const { originalSubmittedTime } = this.getTaskLogColumnData(log);

      // Add basic info row
      const basicInfoData = [
        log.uuid,
        log.role?.role_name || log.role_name || '-',
        log.user?.name || log.user_name || '-',
        log.task?.task_name || log.task_name || '-',
        log.device_name || '-',
        log.branch?.branch_name || log.branch_name || '-',
        log.task_type || '-',
        log.timezone_name || '-',
        log.latitude?.toString() || '-',
        log.longitude?.toString() || '-',
        originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      ];

      // Add row with alternating colors using helper method
      this.addExcelAlternatingRow(worksheet, basicInfoData, currentRow);
      currentRow++;

      // Get task fields for this log
      const taskFields = await this.taskFieldRepository.find({
        where: { log_task_id: log.id },
        relations: ['field_type'],
      });

      // If there are task fields, add them in a nested format
      if (taskFields.length > 0) {
        // Add task fields header
        const fieldHeaderRow = worksheet.addRow(['Task Fields:', '', '']);
        fieldHeaderRow.font = { bold: true, color: { argb: '1A237E' } };
        currentRow++;

        // Add field table headers
        const fieldHeaders = ['Field Name', 'Field Type', 'Value'];
        const fieldTableHeader = worksheet.addRow(fieldHeaders);
        fieldTableHeader.font = { bold: true };
        fieldTableHeader.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E3F2FD' },
        };
        currentRow++;

        // Add task fields
        taskFields.forEach((field, fieldIndex) => {
          const fieldData = [
            field.task_field_name,
            field.field_type_name,
            field.field_type_name === 'image' ||
            field.field_type_name === 'signature'
              ? {
                  text: `View ${field.field_type_name}`,
                  hyperlink: field.field_type_value,
                  tooltip: `Click to view ${field.field_type_name}`,
                }
              : field.field_type_value,
          ];

          const fieldRow = this.addExcelAlternatingRow(worksheet, fieldData, fieldIndex + 1);

          // Add hyperlink styling for image/signature fields
          if (
            field.field_type_name === 'image' ||
            field.field_type_name === 'signature'
          ) {
            const valueCell = fieldRow.getCell(3); // Value column
            valueCell.font = {
              color: { argb: '0000FF' },
              underline: true,
            };
          }

          currentRow++;
        });

        // Add a blank row after task fields
        worksheet.addRow([]);
        currentRow++;
      }

      // Add separator line between task logs if not the last entry
      if (index < data.length - 1) {
        // Add a blank row
        worksheet.addRow([]);
        currentRow++;

        // Add separator line
        const separatorRow = worksheet.addRow(['']);
        for (let i = 1; i <= headers.length; i++) {
          const cell = separatorRow.getCell(i);
          cell.border = {
            bottom: { style: 'thin', color: { argb: 'E0E0E0' } },
          };
        }
        currentRow++;

        // Add another blank row for spacing
        worksheet.addRow([]);
        currentRow++;
      }
    }

    // Set column widths for better layout
    worksheet.getColumn(1).width = 36; // UUID
    worksheet.getColumn(2).width = 20; // Role
    worksheet.getColumn(3).width = 20; // User
    worksheet.getColumn(4).width = 20; // Task
    worksheet.getColumn(5).width = 20; // Device
    worksheet.getColumn(6).width = 20; // Branch
    worksheet.getColumn(7).width = 15; // Task Type
    worksheet.getColumn(8).width = 20; // Timezone
    worksheet.getColumn(9).width = 15; // Latitude
    worksheet.getColumn(10).width = 15; // Longitude
    worksheet.getColumn(11).width = 20; // Original Time

    // Add footer using helper method
    this.addExcelFooter(worksheet, currentRow, 'K');

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: this.generateFilename('task-logs', timezone, 'xlsx'),
    };
  }

  async generatePDFById(
    taskLog: LogTask,
    timezone: Timezone,
  ): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Add Page
    doc.addPage();

    // Add company logo and name in header
    const logoBuffer = await this.loadLogo();
    if (logoBuffer) {
      doc.image(logoBuffer, 50, 50, {
        fit: [50, 50],
      });
    } else {
      // Fallback if logo loading fails
      doc.save().translate(75, 75).rect(-25, -25, 50, 50).fill(this.PRIMARY_COLOR);
    }

    // Add company name next to logo
    doc
      .font('Helvetica-Bold')
      .fontSize(20)
      .fillColor(this.PRIMARY_COLOR)
      .text(this.COMPANY_NAME, 110, 65);

    // Add generation time in header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(`Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`, 350, 65);

    // Add title and header
    this.drawSectionHeader(doc, 'Task Log Details', 120);

    // Add UUID right after header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(`UUID: ${taskLog.uuid}`, 50, 155);

    // Add horizontal line after UUID
    doc
      .moveTo(50, 170)
      .lineTo(545, 170)
      .lineWidth(0.5)
      .strokeColor(this.BORDER_COLOR)
      .stroke();

    // Add entry details with improved layout
    let currentY = 190;

    // Get column data for this task log
    const { leftColumnData, rightColumnData } = this.getTaskLogColumnData(taskLog);

    // Draw table content with alternating background and vertical centering
    leftColumnData.forEach((item, i) => {
      const rowHeight = 30; // Increased row height for better spacing

      // Draw alternating background
      if (i % 2 === 0) {
        doc.fillColor(this.LIGHT_GRAY).rect(50, currentY, 495, rowHeight).fill();
      }

      // Draw left column data
      currentY = this.addFieldRow(
        doc,
        item.label + ':',
        item.value,
        50,
        currentY,
        rowHeight,
      );

      // Draw right column data if available
      if (i < rightColumnData.length) {
        this.addFieldRow(
          doc,
          rightColumnData[i].label + ':',
          rightColumnData[i].value,
          300,
          currentY - rowHeight,
          rowHeight,
        );
      }
    });

    // Add task fields section
    currentY += 20;
    this.drawSectionHeader(doc, 'Task Fields', currentY);
    currentY += 40;

    // Process task fields using helper method
    currentY = await this.processTaskFieldsForPDF(doc, taskLog.id, currentY);

    // Add footer
    const footerWidth = doc.widthOfString(this.SYSTEM_NAME);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        this.SYSTEM_NAME,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: this.generateFilename(`task-log-${taskLog.id}`, timezone, 'pdf'),
        });
      });
    });
  }

  async generateSpreadsheetById(
    taskLog: LogTask,
    timezone: Timezone,
  ): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task Log');

    // Add title and styling
    let currentRow = this.addExcelTitle(worksheet, `${this.COMPANY_NAME} TASK LOG REPORT`, 'A1:G1');
    worksheet.getRow(1).height = 30;

    // Add generation time
    currentRow = this.addExcelGenerationInfo(worksheet, timezone, currentRow, 'A2:G2');

    // Add UUID
    currentRow++;
    worksheet.mergeCells('A3:G3');
    const uuidCell = worksheet.getCell('A3');
    uuidCell.value = `UUID: ${taskLog.uuid}`;
    uuidCell.font = { size: 10, color: { argb: '546E7A' } };
    currentRow++;

    // Add header row for basic info
    this.addExcelHeaderRow(worksheet, ['Field', 'Value']);
    currentRow++;

    // Get column data for this task log
    const { originalSubmittedTime } = this.getTaskLogColumnData(taskLog);

    // Add data rows for basic info
    const dataRows = [
      ['Role', taskLog.role_name || '-'],
      ['User', taskLog.user_name || '-'],
      ['Task', taskLog.task_name || '-'],
      ['Device', taskLog.device_name || '-'],
      ['Branch', taskLog.branch_name || '-'],
      ['Task Type', taskLog.task_type || '-'],
      ['Timezone', taskLog.timezone_name || '-'],
      ['Latitude', taskLog.latitude?.toString() || '-'],
      ['Longitude', taskLog.longitude?.toString() || '-'],
      [
        'Original Time',
        originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      ],
    ];

    // Add data rows with alternating colors using helper method
    dataRows.forEach((row, index) => {
      this.addExcelAlternatingRow(worksheet, row, index);
      currentRow++;
    });

    // Add task fields section
    currentRow += 2;
    const taskFieldsHeaderRow = worksheet.addRow(['Task Fields']);
    taskFieldsHeaderRow.font = {
      size: 14,
      bold: true,
      color: { argb: '1A237E' },
    };
    currentRow++;

    // Add task fields header using helper method
    this.addExcelHeaderRow(worksheet, ['Field Name', 'Field Type', 'Value']);
    currentRow++;

    // Get task fields and add them to the spreadsheet
    const taskFields = await this.taskFieldRepository.find({
      where: { log_task_id: taskLog.id },
      relations: ['field_type'],
    });

    taskFields.forEach((field, index) => {
      const fieldData = [
        field.task_field_name,
        field.field_type_name,
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
          ? {
              text: `View ${field.field_type_name}`,
              hyperlink: field.field_type_value,
              tooltip: `Click to view ${field.field_type_name}`,
            }
          : field.field_type_value,
      ];

      const fieldRow = this.addExcelAlternatingRow(worksheet, fieldData, index + 1);

      // Add hyperlink styling for image/signature fields
      if (
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
      ) {
        const valueCell = fieldRow.getCell(3); // Value column
        valueCell.font = {
          color: { argb: '0000FF' },
          underline: true,
        };
      }

      currentRow++;
    });

    // Set column widths for better layout
    worksheet.getColumn(1).width = 25;
    worksheet.getColumn(2).width = 20;
    worksheet.getColumn(3).width = 50;

    // Add footer using helper method
    this.addExcelFooter(worksheet, currentRow, 'G');

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: this.generateFilename(`task-log-${taskLog.id}`, timezone, 'xlsx'),
    };
  }
}
