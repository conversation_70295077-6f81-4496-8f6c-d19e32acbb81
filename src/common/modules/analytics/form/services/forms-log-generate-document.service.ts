import { Injectable } from '@nestjs/common';
import { Branch } from '../../../database/entities/branch.entity';
import { User } from '../../../database/entities/user.entity';
import { Role } from '../../../database/entities/role.entity';
import { Label } from '../../../database/entities/label.entity';
import { Device } from '../../../database/entities/device.entity';
import { Form } from '../../../database/entities/form.entity';
import axios from 'axios';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import { LogForm } from '../../../database/entities/log-form.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LogForm<PERSON>ield } from '../../../database/entities/log-form-field.entity';
import { Timezone } from '../../../database/entities/timezone.entity';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

// Types for internal data structures
interface FormLogDisplayData {
  label: string;
  value: string;
}

interface PdfBufferSetup {
  doc: PDFKit.PDFDocument;
  buffers: Buffer[];
}

export interface FormsLogFiltersGenerateDocument {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  role: Role | null;
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
  form: Form | null;
}

@Injectable()
export class FormsLogGenerateDocumentService {
  // Constants for consistent styling
  private readonly PRIMARY_COLOR = '#1a237e';
  private readonly SECONDARY_COLOR = '#546e7a';
  private readonly TEXT_COLOR = '#263238';
  private readonly LIGHT_GRAY = '#fafafa';
  private readonly BORDER_COLOR = '#e0e0e0';
  private readonly FOOTER_COLOR = '#9e9e9e';
  private readonly ERROR_COLOR = '#f44336';
  private readonly HYPERLINK_COLOR = '#0000FF';
  private readonly COMPANY_NAME = 'UNIGUARD';
  private readonly LOGO_URL = 'https://trial-serverless.web.app/logo.png';
  private readonly FOOTER_TEXT = 'UniGuard Security System';

  constructor(
    @InjectRepository(LogFormField)
    private formFieldRepository: Repository<LogFormField>,
  ) {}

  /**
   * Generates a PDF document for a single form log entry
   * Creates a professional report with detailed information and form fields
   *
   * @param formLog - Single form log entry to generate report for
   * @param timezone - Timezone information for date formatting
   * @returns Promise resolving to an object containing the PDF buffer and filename
   * @throws {Error} If there's an issue generating the PDF
   */
  async generatePDFById(formLog: LogForm, timezone: Timezone): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    const { doc, buffers } = this.setupPdfDocument();

    // Add Page
    doc.addPage();

    // Add company header
    await this.addCompanyHeader(doc, timezone);

    // Add title and header
    this.drawSectionHeader(doc, 'Form Log Details', 120);

    // Add UUID right after header
    this.addUuidSection(doc, formLog.uuid, 155);

    // Add entry details with improved layout
    let currentY = 190;

    // Transform form log data and render table
    const { leftColumnData, rightColumnData } = this.transformFormLogData(formLog);
    currentY = this.renderDataTable(doc, leftColumnData, rightColumnData, currentY);

    // Add form fields section
    currentY = await this.addFormFieldsSection(doc, formLog.id, currentY);

    // Add footer
    this.addFooter(doc);

    // Finalize and return PDF
    return this.finalizePdfDocument(doc, buffers, 'form-log', formLog.timezone?.timezone_name);
  }



  /**
   * Generates a spreadsheet document for a single form log entry
   * Creates a professional report with detailed information in Excel format
   *
   * @param formLog - Single form log entry to generate report for
   * @param timezone - Timezone information for date formatting
   * @returns Promise resolving to an object containing the spreadsheet buffer and filename
   * @throws {Error} If there's an issue generating the spreadsheet
   */
  async generateSpreadsheetById(formLog: LogForm, timezone: Timezone): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Form Log');

    // Add title and metadata
    let currentRow = this.addExcelTitle(worksheet, 'UNIGUARD FORM LOG REPORT', 'G', 1);
    currentRow = this.addExcelGenerationTime(worksheet, timezone, 'G', currentRow);
    currentRow = this.addExcelUuid(worksheet, formLog.uuid, 'G', currentRow);

    // Add header row
    currentRow = this.addExcelDataHeader(worksheet, ['Field', 'Value'], currentRow);

    // Add data rows
    const dataRows = this.buildFormLogDataRows(formLog);
    currentRow = this.addExcelDataRows(worksheet, dataRows, currentRow);

    // Add form fields section
    currentRow = await this.addExcelFormFieldsSection(worksheet, formLog.id, currentRow);

    // Set column widths and add footer
    this.setExcelColumnWidths(worksheet, [25, 20, 50]);
    this.addExcelFooter(worksheet, 'G', currentRow + 1);

    // Generate and return buffer
    return this.finalizeExcelDocument(workbook, `form-log-${formLog.id}`, formLog.timezone?.timezone_name);
  }

  /**
   * Generates a PDF document from multiple form log entries
   * Creates a professional report with cover page and detailed form entries
   *
   * @param data - Array of form log entries
   * @param filters - Object containing all applied filters
   * @param timezone - Timezone information for date formatting
   * @returns Promise resolving to an object containing the PDF buffer and filename
   * @throws {Error} If there's an issue generating the PDF
   */
  async generatePDF(
    data: LogForm[],
    filters: FormsLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    const { doc, buffers } = this.setupPdfDocument();

    // Add cover page with background and company branding
    await this.addCoverPage(doc, 'FORM LOG REPORT');

    // Add filters and metadata section
    this.addFiltersSection(doc, filters, timezone, data.length, 300);

    // Add footer to cover page
    this.addFooter(doc);

    // Process each form log entry
    for (let index = 0; index < data.length; index++) {
      await this.addFormLogPage(doc, data[index], index, data.length);
    }

    // Finalize and return PDF
    return this.finalizePdfDocument(doc, buffers, 'form-logs', data.length > 0 ? data[0].timezone?.timezone_name : timezone.timezone_name);
  }

  /**
   * Generates a spreadsheet document from multiple form log entries
   * Creates a professional report with detailed information in Excel format
   *
   * @param data - Array of form log entries
   * @param filters - Object containing all applied filters
   * @param timezone - Timezone information for date formatting
   * @returns Promise resolving to an object containing the spreadsheet buffer and filename
   * @throws {Error} If there's an issue generating the spreadsheet
   */
  async generateSpreadsheet(
    data: LogForm[],
    filters: FormsLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Form Logs');
    let currentRow = 1;

    // Add title and metadata
    currentRow = this.addExcelTitle(worksheet, 'UNIGUARD FORM LOGS REPORT', 'K', currentRow);
    currentRow = this.addExcelGenerationTimeWithTotal(worksheet, timezone, data.length, 'K', currentRow);

    // Add filter information
    currentRow = this.addExcelFiltersSection(worksheet, filters, currentRow);

    // Add main data section
    currentRow = this.addExcelMainDataHeader(worksheet, 'Form Logs Data', currentRow);

    // Add table headers
    const headers = [
      'UUID', 'Role', 'User', 'Form', 'Device',
      'Timezone', 'Latitude', 'Longitude', 'Original Time'
    ];
    currentRow = this.addExcelTableHeaders(worksheet, headers, currentRow);

    // Process each form log
    for (const [index, log] of data.entries()) {
      currentRow = await this.addExcelFormLogEntry(worksheet, log, index, currentRow);
    }

    // Set column widths and add footer
    this.setExcelFormLogColumnWidths(worksheet);
    this.addExcelFooter(worksheet, 'K', currentRow + 1);

    // Generate and return buffer
    return this.finalizeExcelDocument(workbook, 'form-logs', data.length > 0 ? data[0].timezone?.timezone_name : timezone.timezone_name);
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Sets up PDF document with buffer handling
   */
  private setupPdfDocument(): PdfBufferSetup {
    const doc = this.createPdfDoc();
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));
    return { doc, buffers };
  }

  /**
   * Creates a new PDF document with standard configuration
   */
  private createPdfDoc(): PDFKit.PDFDocument {
    return new PDFDocument({
      size: 'A4',
      autoFirstPage: false,
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      info: {
        Title: 'Forms Log Report',
        Author: 'UniGuard System',
        Creator: 'UniGuard',
      },
    });
  }

  /**
   * Draws a section header with underline
   */
  private drawSectionHeader(doc: PDFKit.PDFDocument, text: string, y: number): void {
    doc
      .fontSize(16)
      .font('Helvetica-Bold')
      .fillColor(this.PRIMARY_COLOR)
      .text(text, 50, y);

    doc
      .moveTo(50, y + 25)
      .lineTo(545, y + 25)
      .lineWidth(1)
      .strokeColor(this.PRIMARY_COLOR)
      .stroke();
  }

  /**
   * Adds a field row with label and value
   */
  private addFieldRow(
    doc: PDFKit.PDFDocument,
    label: string,
    value: string,
    x: number,
    y: number,
    rowHeight: number = 25,
  ): number {
    const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
    const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

    doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(label, x, labelY);

    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.TEXT_COLOR)
      .text(value || '-', x + 150, valueY);

    return y + rowHeight;
  }

  /**
   * Loads company logo from URL with fallback
   */
  private async loadLogo(): Promise<Buffer | null> {
    try {
      const response = await axios.get(this.LOGO_URL, {
        responseType: 'arraybuffer',
      });
      return Buffer.from(response.data, 'binary');
    } catch (error) {
      console.error('Error loading logo:', error);
      return null;
    }
  }

  /**
   * Adds company header with logo and generation time
   */
  private async addCompanyHeader(doc: PDFKit.PDFDocument, timezone: Timezone): Promise<void> {
    const logoBuffer = await this.loadLogo();

    if (logoBuffer) {
      doc.image(logoBuffer, 50, 50, { fit: [50, 50] });
    } else {
      // Fallback if logo loading fails
      doc.save().translate(75, 75).rect(-25, -25, 50, 50).fill(this.PRIMARY_COLOR);
    }

    // Add company name next to logo
    doc
      .font('Helvetica-Bold')
      .fontSize(20)
      .fillColor(this.PRIMARY_COLOR)
      .text(this.COMPANY_NAME, 110, 65);

    // Add generation time in header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(
        `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`,
        350,
        65,
      );
  }

  /**
   * Adds UUID section with horizontal line
   */
  private addUuidSection(doc: PDFKit.PDFDocument, uuid: string, y: number): void {
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(`UUID: ${uuid}`, 50, y);

    // Add horizontal line after UUID
    doc
      .moveTo(50, y + 15)
      .lineTo(545, y + 15)
      .lineWidth(0.5)
      .strokeColor(this.BORDER_COLOR)
      .stroke();
  }

  /**
   * Transforms form log data into display format
   */
  private transformFormLogData(formLog: LogForm): {
    leftColumnData: FormLogDisplayData[];
    rightColumnData: FormLogDisplayData[];
  } {
    const originalSubmittedTime = dayjs(formLog.original_submitted_time).tz(
      formLog.timezone_name,
    );

    const leftColumnData = [
      { label: 'Role', value: formLog.role_name },
      { label: 'User', value: formLog.user_name },
      { label: 'Form', value: formLog.form_name },
      { label: 'Device', value: formLog.device_name },
    ];

    const rightColumnData = [
      { label: 'Timezone', value: formLog.timezone_name },
      { label: 'Latitude', value: formLog.latitude?.toString() },
      { label: 'Longitude', value: formLog.longitude?.toString() },
      {
        label: 'Original Time',
        value: originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      },
    ];

    return { leftColumnData, rightColumnData };
  }

  /**
   * Renders data table with alternating background colors
   */
  private renderDataTable(
    doc: PDFKit.PDFDocument,
    leftColumnData: FormLogDisplayData[],
    rightColumnData: FormLogDisplayData[],
    startY: number,
  ): number {
    let currentY = startY;
    const rowHeight = 30;

    leftColumnData.forEach((item, i) => {
      // Draw alternating background
      if (i % 2 === 0) {
        doc.fillColor(this.LIGHT_GRAY).rect(50, currentY, 495, rowHeight).fill();
      }

      // Draw left column data
      currentY = this.addFieldRow(
        doc,
        item.label + ':',
        item.value,
        50,
        currentY,
        rowHeight,
      );

      // Draw right column data if available
      if (i < rightColumnData.length) {
        this.addFieldRow(
          doc,
          rightColumnData[i].label + ':',
          rightColumnData[i].value,
          300,
          currentY - rowHeight,
          rowHeight,
        );
      }
    });

    return currentY;
  }

  /**
   * Adds form fields section to PDF
   */
  private async addFormFieldsSection(doc: PDFKit.PDFDocument, logFormId: number, startY: number): Promise<number> {
    let currentY = startY + 20;
    this.drawSectionHeader(doc, 'Form Fields', currentY);
    currentY += 40;

    // Get form fields and add them to the PDF
    const formFields = await this.formFieldRepository.find({
      where: { log_form_id: logFormId },
      relations: ['field_type'],
    });

    for (const [index, field] of formFields.entries()) {
      const rowHeight = 30;
      if (index % 2 === 0) {
        doc.fillColor(this.LIGHT_GRAY).rect(50, currentY, 495, rowHeight).fill();
      }

      // Handle image and signature fields differently
      if (field.field_type_name === 'image' || field.field_type_name === 'signature') {
        currentY = await this.addImageField(doc, field, currentY, rowHeight);
      } else {
        // Regular field handling
        currentY = this.addFieldRow(
          doc,
          field.form_field_name + ':',
          field.field_type_value,
          50,
          currentY,
          rowHeight,
        );
      }
    }

    return currentY;
  }

  /**
   * Adds image field to PDF with error handling
   */
  private async addImageField(doc: PDFKit.PDFDocument, field: LogFormField, currentY: number, rowHeight: number): Promise<number> {
    // Add field name
    doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .fillColor(this.SECONDARY_COLOR)
      .text(
        field.form_field_name + ':',
        50,
        currentY + (rowHeight - doc.currentLineHeight()) / 2,
      );

    try {
      // Try to load and add the image
      const response = await axios.get(field.field_type_value, {
        responseType: 'arraybuffer',
      });
      const imageBuffer = Buffer.from(response.data, 'binary');

      // Add some spacing after field name
      currentY += rowHeight;

      // Add the image with reasonable dimensions
      doc.image(imageBuffer, 50, currentY, {
        fit: [200, 100],
        align: 'center',
      });

      // Add extra space after image
      return currentY + 100;
    } catch (error) {
      // If image loading fails, show error message
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor(this.ERROR_COLOR)
        .text(
          'Failed to load image',
          200,
          currentY + (rowHeight - doc.currentLineHeight()) / 2,
        );
      return currentY + rowHeight;
    }
  }

  /**
   * Adds footer to PDF
   */
  private addFooter(doc: PDFKit.PDFDocument): void {
    const footerWidth = doc.widthOfString(this.FOOTER_TEXT);
    doc
      .fontSize(10)
      .fillColor(this.FOOTER_COLOR)
      .text(
        this.FOOTER_TEXT,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );
  }

  /**
   * Finalizes PDF document and returns buffer with filename
   */
  private finalizePdfDocument(
    doc: PDFKit.PDFDocument,
    buffers: Buffer[],
    prefix: string,
    timezoneName?: string,
  ): Promise<{ buffer: Buffer; filename: string }> {
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        const timestamp = dayjs().tz(timezoneName || 'UTC').format('YYYYMMDD-HHmmss');
        resolve({
          buffer: buffer,
          filename: `${prefix}-${timestamp}.pdf`,
        });
      });
    });
  }

  /**
   * Adds cover page with background and company branding
   */
  private async addCoverPage(doc: PDFKit.PDFDocument, title: string): Promise<void> {
    doc.addPage();

    // Add background
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    // Add company logo and name
    const logoBuffer = await this.loadLogo();

    if (logoBuffer) {
      doc.image(logoBuffer, (doc.page.width - 80) / 2, 100, { fit: [80, 80] });
    } else {
      // Fallback if logo loading fails
      doc
        .save()
        .translate(doc.page.width / 2, 140)
        .rect(-40, -40, 80, 80)
        .fill(this.PRIMARY_COLOR);
    }

    // Add company name
    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor(this.PRIMARY_COLOR)
      .text(
        this.COMPANY_NAME,
        (doc.page.width - doc.widthOfString(this.COMPANY_NAME)) / 2,
        190,
      );

    // Add title
    doc.font('Helvetica-Bold').fontSize(18).fillColor(this.PRIMARY_COLOR);
    const titleWidth = doc.widthOfString(title);
    doc.text(title, (doc.page.width - titleWidth) / 2, 240);
  }

  /**
   * Adds filters section to PDF cover page
   */
  private addFiltersSection(
    doc: PDFKit.PDFDocument,
    filters: FormsLogFiltersGenerateDocument,
    timezone: Timezone,
    totalEntries: number,
    startY: number,
  ): void {
    const metadataY = startY;
    doc.font('Helvetica').fontSize(12).fillColor(this.SECONDARY_COLOR);

    // Add active filters section
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor(this.PRIMARY_COLOR)
      .text('Active Filters', 50, metadataY);

    doc
      .moveTo(50, metadataY + 25)
      .lineTo(545, metadataY + 25)
      .lineWidth(1)
      .strokeColor(this.PRIMARY_COLOR)
      .stroke();

    let filterY = metadataY + 30;

    // Build filter data
    const leftFilters = this.buildLeftFilters(filters);
    const rightFilters = this.buildRightFilters(filters);

    // Render filters in two columns
    filterY = this.renderFiltersInColumns(doc, leftFilters, rightFilters, filterY);

    // Add generation info
    filterY = this.addGenerationInfo(doc, timezone, totalEntries, filterY);

    // Add date range explanation if applicable
    if (filters.startDate && filters.endDate) {
      this.addDateRangeExplanation(doc, filters, filterY);
    }
  }

  /**
   * Builds left column filter data
   */
  private buildLeftFilters(filters: FormsLogFiltersGenerateDocument) {
    return [
      {
        label: 'Start Date:',
        value: filters.startDate
          ? dayjs(filters.startDate).format('MMMM D, YYYY')
          : 'All Dates',
      },
      {
        label: 'End Date:',
        value: filters.endDate
          ? dayjs(filters.endDate).format('MMMM D, YYYY')
          : 'All Dates',
      },
      {
        label: 'User:',
        value: filters.user ? filters.user.name : 'All Users',
      },
      {
        label: 'Device:',
        value: filters.device ? filters.device.device_name : 'All Devices',
      },
      {
        label: 'Form:',
        value: filters.form ? filters.form.form_name : 'All Forms',
      },
    ];
  }

  /**
   * Builds right column filter data
   */
  private buildRightFilters(filters: FormsLogFiltersGenerateDocument) {
    return [
      {
        label: 'Start Time:',
        value: filters.startTime || 'All Times',
      },
      {
        label: 'End Time:',
        value: filters.endTime || 'All Times',
      },
      {
        label: 'Role:',
        value: filters.role ? filters.role.role_name : 'All Roles',
      },
      {
        label: 'User Labels:',
        value: filters.user_labels && filters.user_labels.length > 0
          ? filters.user_labels.map(label => label.label_name).join(', ')
          : 'All Labels',
      },
      {
        label: 'Device Labels:',
        value: filters.device_labels && filters.device_labels.length > 0
          ? filters.device_labels.map(label => label.label_name).join(', ')
          : 'All Labels',
      },
    ];
  }

  /**
   * Renders filters in two columns
   */
  private renderFiltersInColumns(
    doc: PDFKit.PDFDocument,
    leftFilters: any[],
    rightFilters: any[],
    startY: number,
  ): number {
    const rowHeight = 25;
    const leftX = 50;
    const rightX = 300;
    let filterY = startY;

    const maxRows = Math.max(leftFilters.length, rightFilters.length);
    for (let i = 0; i < maxRows; i++) {
      // Add left column filter
      if (i < leftFilters.length) {
        const leftFilter = leftFilters[i];
        this.addFieldRow(
          doc,
          leftFilter.label,
          leftFilter.value,
          leftX,
          filterY,
          rowHeight,
        );
      }

      // Add right column filter
      if (i < rightFilters.length) {
        const rightFilter = rightFilters[i];
        this.addFieldRow(
          doc,
          rightFilter.label,
          rightFilter.value,
          rightX,
          filterY,
          rowHeight,
        );
      }

      filterY += rowHeight;
    }

    return filterY;
  }

  /**
   * Adds generation info to PDF
   */
  private addGenerationInfo(
    doc: PDFKit.PDFDocument,
    timezone: Timezone,
    totalEntries: number,
    startY: number,
  ): number {
    doc.font('Helvetica').fontSize(12).fillColor(this.SECONDARY_COLOR);

    let filterY = this.addFieldRow(
      doc,
      'Report Generated:',
      dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm'),
      50,
      startY,
    );
    filterY = this.addFieldRow(
      doc,
      'Total Entries:',
      totalEntries.toString(),
      50,
      filterY,
    );

    return filterY;
  }

  /**
   * Adds date range explanation to PDF
   */
  private addDateRangeExplanation(
    doc: PDFKit.PDFDocument,
    filters: FormsLogFiltersGenerateDocument,
    startY: number,
  ): void {
    let filterY = startY + 20;
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor(this.PRIMARY_COLOR)
      .text('Data From:', 50, filterY);

    filterY += 25;

    // Calculate all days between start and end date
    const startDate = dayjs(filters.startDate);
    const endDate = dayjs(filters.endDate);
    const daysDiff = endDate.diff(startDate, 'day') + 1;
    const startTime = filters.startTime || '00:00';
    const endTime = filters.endTime || '23:59';

    // Display up to 3 days with truncation if more
    const maxDaysToShow = 3;
    const daysToShow = Math.min(daysDiff, maxDaysToShow);

    for (let i = 0; i < daysToShow; i++) {
      const currentDate = startDate.add(i, 'day');
      const dateStr = currentDate.format('YYYY-MM-DD');
      const timeRangeStr = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor(this.TEXT_COLOR)
        .text(timeRangeStr, 50, filterY + i * 20);
    }

    // Add truncation indicator if there are more days
    if (daysDiff > maxDaysToShow) {
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor(this.TEXT_COLOR)
        .text('...', 50, filterY + maxDaysToShow * 20);

      // Show the last day
      const lastDate = endDate.format('YYYY-MM-DD');
      const lastTimeRangeStr = `${lastDate} ${startTime} until ${lastDate} ${endTime}`;

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor(this.TEXT_COLOR)
        .text(lastTimeRangeStr, 50, filterY + (maxDaysToShow + 1) * 20);
    }
  }

  /**
   * Adds form log page to PDF
   */
  private async addFormLogPage(doc: PDFKit.PDFDocument, log: LogForm, index: number, totalEntries: number): Promise<void> {
    doc.addPage();

    // Add page header with UUID
    this.drawSectionHeader(doc, `Form Log Entry #${index + 1}`, 50);

    // Add UUID right after header
    this.addUuidSection(doc, log.uuid, 85);

    // Add entry details with improved layout
    let currentY = 120;

    // Transform form log data and render table
    const { leftColumnData, rightColumnData } = this.transformFormLogData(log);
    currentY = this.renderDataTable(doc, leftColumnData, rightColumnData, currentY);

    // Add form fields section
    currentY = await this.addFormFieldsSection(doc, log.id, currentY);

    // Add page number at fixed position from bottom
    const pageText = `Page ${index + 2} of ${totalEntries + 1}`;
    const pageWidth = doc.widthOfString(pageText);
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor(this.FOOTER_COLOR)
      .text(pageText, (doc.page.width - pageWidth) / 2, doc.page.height - 30);
  }

  // ===== EXCEL HELPER METHODS =====

  /**
   * Adds Excel title with merged cells
   */
  private addExcelTitle(worksheet: ExcelJS.Worksheet, title: string, mergeToColumn: string, currentRow: number): number {
    worksheet.mergeCells(`A${currentRow}:${mergeToColumn}${currentRow}`);
    const titleCell = worksheet.getCell(`A${currentRow}`);
    titleCell.value = title;
    titleCell.font = { size: 16, bold: true, color: { argb: this.PRIMARY_COLOR.replace('#', '') } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(currentRow).height = 30;
    return currentRow + 1;
  }

  /**
   * Adds Excel generation time
   */
  private addExcelGenerationTime(worksheet: ExcelJS.Worksheet, timezone: Timezone, mergeToColumn: string, currentRow: number): number {
    worksheet.mergeCells(`A${currentRow}:${mergeToColumn}${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`;
    timeCell.font = { size: 10, color: { argb: this.SECONDARY_COLOR.replace('#', '') } };
    timeCell.alignment = { horizontal: 'center' };
    return currentRow + 1;
  }

  /**
   * Adds Excel generation time with total entries
   */
  private addExcelGenerationTimeWithTotal(worksheet: ExcelJS.Worksheet, timezone: Timezone, totalEntries: number, mergeToColumn: string, currentRow: number): number {
    worksheet.mergeCells(`A${currentRow}:${mergeToColumn}${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')} | Total Entries: ${totalEntries}`;
    timeCell.font = { size: 10, color: { argb: this.SECONDARY_COLOR.replace('#', '') } };
    timeCell.alignment = { horizontal: 'center' };
    return currentRow + 1;
  }

  /**
   * Adds Excel UUID section
   */
  private addExcelUuid(worksheet: ExcelJS.Worksheet, uuid: string, mergeToColumn: string, currentRow: number): number {
    worksheet.mergeCells(`A${currentRow}:${mergeToColumn}${currentRow}`);
    const uuidCell = worksheet.getCell(`A${currentRow}`);
    uuidCell.value = `UUID: ${uuid}`;
    uuidCell.font = { size: 10, color: { argb: this.SECONDARY_COLOR.replace('#', '') } };
    return currentRow + 1;
  }

  /**
   * Adds Excel data header
   */
  private addExcelDataHeader(worksheet: ExcelJS.Worksheet, headers: string[], currentRow: number): number {
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    return currentRow + 1;
  }

  /**
   * Builds form log data rows for Excel
   */
  private buildFormLogDataRows(formLog: LogForm): string[][] {
    return [
      ['Role', formLog.role_name || '-'],
      ['User', formLog.user_name || '-'],
      ['Form', formLog.form_name || '-'],
      ['Device', formLog.device_name || '-'],
      ['Timezone', formLog.timezone_name || '-'],
      ['Latitude', formLog.latitude?.toString() || '-'],
      ['Longitude', formLog.longitude?.toString() || '-'],
      [
        'Original Time',
        dayjs(formLog.original_submitted_time).tz(formLog.timezone_name).format('YYYY-MM-DD HH:mm'),
      ],
    ];
  }

  /**
   * Adds Excel data rows with alternating colors
   */
  private addExcelDataRows(worksheet: ExcelJS.Worksheet, dataRows: string[][], currentRow: number): number {
    dataRows.forEach((row, index) => {
      currentRow++;
      const excelRow = worksheet.addRow(row);
      // Add alternating row colors for better readability
      if (index % 2 === 1) {
        excelRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: this.LIGHT_GRAY.replace('#', '') },
        };
      }
    });
    return currentRow;
  }

  /**
   * Adds Excel form fields section
   */
  private async addExcelFormFieldsSection(worksheet: ExcelJS.Worksheet, logFormId: number, currentRow: number): Promise<number> {
    // Add form fields section
    currentRow += 2;
    const formFieldsHeaderRow = worksheet.addRow(['Form Fields']);
    formFieldsHeaderRow.font = {
      size: 14,
      bold: true,
      color: { argb: this.PRIMARY_COLOR.replace('#', '') },
    };
    currentRow++;

    // Add form fields header
    const formFieldsTableHeader = worksheet.addRow([
      'Field Name',
      'Field Type',
      'Value',
    ]);
    formFieldsTableHeader.font = { bold: true };
    formFieldsTableHeader.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    currentRow++;

    // Get form fields and add them to the spreadsheet
    const formFields = await this.formFieldRepository.find({
      where: { log_form_id: logFormId },
      relations: ['field_type'],
    });

    formFields.forEach((field, index) => {
      const fieldRow = worksheet.addRow([
        field.form_field_name,
        field.field_type_name,
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
          ? {
              text: `View ${field.field_type_name}`,
              hyperlink: field.field_type_value,
              tooltip: `Click to view ${field.field_type_name}`,
            }
          : field.field_type_value,
      ]);

      if (index % 2 === 1) {
        fieldRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: this.LIGHT_GRAY.replace('#', '') },
        };
      }

      // Add hyperlink styling for image/signature fields
      if (
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
      ) {
        const valueCell = fieldRow.getCell(3); // Value column
        valueCell.font = {
          color: { argb: this.HYPERLINK_COLOR.replace('#', '') },
          underline: true,
        };
      }

      currentRow++;
    });

    return currentRow;
  }

  /**
   * Sets Excel column widths
   */
  private setExcelColumnWidths(worksheet: ExcelJS.Worksheet, widths: number[]): void {
    widths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });
  }

  /**
   * Sets Excel form log column widths
   */
  private setExcelFormLogColumnWidths(worksheet: ExcelJS.Worksheet): void {
    worksheet.getColumn(1).width = 36; // UUID
    worksheet.getColumn(2).width = 20; // Role
    worksheet.getColumn(3).width = 20; // User
    worksheet.getColumn(4).width = 20; // Form
    worksheet.getColumn(5).width = 20; // Device
    worksheet.getColumn(6).width = 20; // Timezone
    worksheet.getColumn(7).width = 15; // Latitude
    worksheet.getColumn(8).width = 15; // Longitude
    worksheet.getColumn(9).width = 20; // Original Time
  }

  /**
   * Adds Excel filters section
   */
  private addExcelFiltersSection(worksheet: ExcelJS.Worksheet, filters: FormsLogFiltersGenerateDocument, currentRow: number): number {
    // Add filter information
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: this.PRIMARY_COLOR.replace('#', '') } };

    // Add filter details
    const filterMappings = [
      { condition: filters.startDate && filters.endDate, label: 'Date Range:', value: `${filters.startDate} to ${filters.endDate}` },
      { condition: filters.startTime && filters.endTime, label: 'Time Range:', value: `${filters.startTime} to ${filters.endTime}` },
      { condition: filters.role, label: 'Role:', value: filters.role?.role_name },
      { condition: filters.user, label: 'User:', value: filters.user?.name },
      { condition: filters.user_labels.length > 0, label: 'User Labels:', value: filters.user_labels.map(label => label.label_name).join(', ') },
      { condition: filters.device, label: 'Device:', value: filters.device?.device_name },
      { condition: filters.device_labels.length > 0, label: 'Device Labels:', value: filters.device_labels.map(label => label.label_name).join(', ') },
      { condition: filters.form, label: 'Form:', value: filters.form?.form_name },
    ];

    filterMappings.forEach(filter => {
      if (filter.condition) {
        currentRow++;
        worksheet.getCell(`A${currentRow}`).value = filter.label;
        worksheet.getCell(`B${currentRow}`).value = filter.value;
        worksheet.getCell(`A${currentRow}`).font = { bold: true };
      }
    });

    return currentRow;
  }

  /**
   * Adds Excel main data header
   */
  private addExcelMainDataHeader(worksheet: ExcelJS.Worksheet, title: string, currentRow: number): number {
    currentRow += 2;
    const mainDataHeaderRow = worksheet.addRow([title]);
    mainDataHeaderRow.font = {
      size: 14,
      bold: true,
      color: { argb: this.PRIMARY_COLOR.replace('#', '') },
    };
    return currentRow + 1;
  }

  /**
   * Adds Excel table headers
   */
  private addExcelTableHeaders(worksheet: ExcelJS.Worksheet, headers: string[], currentRow: number): number {
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: this.PRIMARY_COLOR.replace('#', '') },
    };
    headerRow.alignment = { horizontal: 'center' };
    return currentRow + 1;
  }

  /**
   * Adds Excel form log entry
   */
  private async addExcelFormLogEntry(worksheet: ExcelJS.Worksheet, log: LogForm, index: number, currentRow: number): Promise<number> {
    // Add basic info row
    const basicInfoRow = worksheet.addRow([
      log.uuid,
      log.role?.role_name || '-',
      log.user?.name || '-',
      log.form?.form_name || '-',
      log.device?.device_name || '-',
      log.timezone?.timezone_name || '-',
      log.latitude?.toString() || '-',
      log.longitude?.toString() || '-',
      dayjs(log.original_submitted_time).tz(log.timezone_name).format('YYYY-MM-DD HH:mm'),
    ]);

    // Add alternating colors for better readability
    if (currentRow % 2 === 0) {
      basicInfoRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'F5F5F5' },
      };
    }
    currentRow++;

    // Get form fields for this log
    const formFields = await this.formFieldRepository.find({
      where: { log_form_id: log.id },
      relations: ['field_type'],
    });

    // If there are form fields, add them in a nested format
    if (formFields.length > 0) {
      // Add form fields header
      const fieldHeaderRow = worksheet.addRow(['Form Fields:', '', '']);
      fieldHeaderRow.font = { bold: true, color: { argb: this.PRIMARY_COLOR.replace('#', '') } };
      currentRow++;

      const fieldTableHeader = worksheet.addRow(['Field Name', 'Field Type', 'Value']);
      fieldTableHeader.font = { bold: true };
      fieldTableHeader.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'E3F2FD' },
      };
      currentRow++;

      // Add form fields
      formFields.forEach((field, fieldIndex) => {
        const fieldRow = worksheet.addRow([
          field.form_field_name,
          field.field_type_name,
          field.field_type_name === 'image' ||
          field.field_type_name === 'signature'
            ? {
                text: `View ${field.field_type_name}`,
                hyperlink: field.field_type_value,
                tooltip: `Click to view ${field.field_type_name}`,
              }
            : field.field_type_value,
        ]);

        if (fieldIndex % 2 === 1) {
          fieldRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: this.LIGHT_GRAY.replace('#', '') },
          };
        }

        // Add hyperlink styling for image/signature fields
        if (
          field.field_type_name === 'image' ||
          field.field_type_name === 'signature'
        ) {
          const valueCell = fieldRow.getCell(3); // Value column
          valueCell.font = {
            color: { argb: this.HYPERLINK_COLOR.replace('#', '') },
            underline: true,
          };
        }

        currentRow++;
      });

      // Add a blank row after form fields
      worksheet.addRow([]);
      currentRow++;
    }

    return currentRow;
  }

  /**
   * Adds Excel footer
   */
  private addExcelFooter(worksheet: ExcelJS.Worksheet, mergeToColumn: string, currentRow: number): void {
    currentRow += 2;
    worksheet.mergeCells(`A${currentRow}:${mergeToColumn}${currentRow}`);
    const footerCell = worksheet.getCell(`A${currentRow}`);
    footerCell.value = this.FOOTER_TEXT;
    footerCell.font = { size: 10, color: { argb: this.FOOTER_COLOR.replace('#', '') } };
    footerCell.alignment = { horizontal: 'center' };
  }

  /**
   * Finalizes Excel document and returns buffer with filename
   */
  private async finalizeExcelDocument(
    workbook: ExcelJS.Workbook,
    prefix: string,
    timezoneName?: string,
  ): Promise<{ buffer: Buffer; filename: string }> {
    const buffer = await workbook.xlsx.writeBuffer();
    const timestamp = dayjs().tz(timezoneName || 'UTC').format('YYYYMMDD-HHmmss');

    return {
      buffer: buffer as Buffer,
      filename: `${prefix}-${timestamp}.xlsx`,
    };
  }
}
